// 车辆监控相关类型定义

/**
 * 公交站点信息
 */
export interface BusStation {
  /** 站点ID */
  stationId: number | string
  /** 站点名称 */
  stationName: string
  /** 站点编号 */
  stationCode: string
  /** 站点顺序（在线路中的位置） */
  sequence: number
  /** 站点坐标 */
  coordinates: {
    x: number
    y: number
  }
  /** 站点类型 */
  stationType: 'normal' | 'transfer' | 'terminal'
  /** 是否为关键站点 */
  isKeyStation: boolean
}

/**
 * 公交线路信息
 */
export interface BusRoute {
  /** 线路ID */
  routeId: number | string
  /** 线路名称 */
  routeName: string
  /** 线路编号 */
  routeCode: string
  /** 线路描述 */
  description?: string
  /** 线路颜色 */
  color: string
  /** 站点列表 */
  stations: BusStation[]
  /** 线路总长度（公里） */
  totalDistance: number
  /** 运营状态 */
  operationStatus: 'active' | 'inactive' | 'maintenance'
}

/**
 * 车辆位置信息
 */
export interface VehiclePosition {
  /** 车辆ID */
  vehicleId: number | string
  /** 车牌号 */
  plateNumber: string
  /** 当前位置坐标 */
  coordinates: {
    x: number
    y: number
  }
  /** 当前所在站点ID（如果在站点） */
  currentStationId?: number | string
  /** 下一站点ID */
  nextStationId?: number | string
  /** 行驶方向 */
  direction: 'forward' | 'backward'
  /** 车辆状态 */
  status: 'running' | 'stopped' | 'maintenance' | 'offline'
  /** 速度（km/h） */
  speed: number
  /** 载客数量 */
  passengerCount: number
  /** 最大载客量 */
  maxCapacity: number
  /** 最后更新时间 */
  lastUpdateTime: string
}

/**
 * 车辆监控数据
 */
export interface VehicleMonitorData {
  /** 选中的线路 */
  selectedRoute: BusRoute | null
  /** 线路上的车辆位置列表 */
  vehiclePositions: VehiclePosition[]
  /** 监控状态 */
  monitorStatus: 'active' | 'paused' | 'error'
  /** 最后更新时间 */
  lastUpdateTime: string
}

/**
 * 监控配置
 */
export interface MonitorConfig {
  /** 自动刷新间隔（秒） */
  refreshInterval: number
  /** 是否显示车辆轨迹 */
  showVehicleTrail: boolean
  /** 是否显示站点名称 */
  showStationNames: boolean
  /** 是否显示车辆信息 */
  showVehicleInfo: boolean
  /** 地图缩放级别 */
  zoomLevel: number
}

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 消息 */
  message: string
  /** 数据 */
  data: T
  /** 是否成功 */
  success: boolean
}
