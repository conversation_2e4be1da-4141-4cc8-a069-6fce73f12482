<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>大屏展示</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>
      
      <div class="screen-content">
        <el-alert
          title="大屏展示"
          description="大屏幕展示线路和车辆信息。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="大屏展示功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="DisplayScreen">
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.screen-content {
  padding: 20px;
}
</style>
