<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>调度指令</span>
          <div>
            <el-button type="primary" @click="handleVoice">语音功能</el-button>
            <el-button type="danger" @click="handleEmergency">紧急调度</el-button>
          </div>
        </div>
      </template>
      
      <div class="dispatch-content">
        <el-alert
          title="调度指令中心"
          description="发送语音指令和处理紧急调度情况。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="调度指令功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="Dispatch">
const { proxy } = getCurrentInstance();

function handleVoice() {
  proxy.$router.push('/monitor/dispatch/voice');
}

function handleEmergency() {
  proxy.$router.push('/monitor/dispatch/emergency');
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dispatch-content {
  padding: 20px;
}
</style>
