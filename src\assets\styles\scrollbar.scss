/**
 * 自定义滚动条样式
 * 基于 OverlayScrollbars 的主题定制
 */

// 导入 OverlayScrollbars 基础样式
@import 'overlayscrollbars/overlayscrollbars.css';

// 浅色主题滚动条样式
.os-theme-light {
  --os-size: 8px;
  --os-padding-perpendicular: 2px;
  --os-padding-axis: 2px;
  --os-track-border-radius: 4px;
  --os-track-bg: transparent;
  --os-track-bg-hover: rgba(0, 0, 0, 0.05);
  --os-track-bg-active: rgba(0, 0, 0, 0.08);
  --os-track-border: transparent;
  --os-handle-border-radius: 4px;
  --os-handle-bg: rgba(0, 0, 0, 0.2);
  --os-handle-bg-hover: rgba(0, 0, 0, 0.35);
  --os-handle-bg-active: rgba(0, 0, 0, 0.5);
  --os-handle-border: transparent;
  --os-handle-min-size: 30px;
  --os-handle-max-size: none;
  --os-handle-perpendicular-size: 100%;
  --os-handle-perpendicular-size-hover: 100%;
  --os-handle-interactive-area-offset: 0px;
}

// 深色主题滚动条样式
.os-theme-dark {
  --os-size: 8px;
  --os-padding-perpendicular: 2px;
  --os-padding-axis: 2px;
  --os-track-border-radius: 4px;
  --os-track-bg: transparent;
  --os-track-bg-hover: rgba(255, 255, 255, 0.08);
  --os-track-bg-active: rgba(255, 255, 255, 0.12);
  --os-track-border: transparent;
  --os-handle-border-radius: 4px;
  --os-handle-bg: rgba(255, 255, 255, 0.3);
  --os-handle-bg-hover: rgba(255, 255, 255, 0.45);
  --os-handle-bg-active: rgba(255, 255, 255, 0.6);
  --os-handle-border: transparent;
  --os-handle-min-size: 30px;
  --os-handle-max-size: none;
  --os-handle-perpendicular-size: 100%;
  --os-handle-perpendicular-size-hover: 100%;
  --os-handle-interactive-area-offset: 0px;
}

// 针对 .content-area 的特殊样式
.content-area {
  // 确保 OverlayScrollbars 正确应用
  &.os-host {
    .os-viewport {
      // 保持内容区域的原有样式
      padding: inherit;
    }
    
    // 滚动条轨道样式优化
    .os-scrollbar-vertical {
      right: 2px;
      width: 8px;
      
      .os-scrollbar-track {
        background: transparent;
        border-radius: 4px;
        
        &:hover {
          background: var(--os-track-bg-hover);
        }
      }
      
      .os-scrollbar-handle {
        border-radius: 4px;
        min-height: 30px;
        background: var(--os-handle-bg);
        transition: all 0.2s ease;
        
        &:hover {
          background: var(--os-handle-bg-hover);
        }
        
        &:active {
          background: var(--os-handle-bg-active);
        }
      }
    }
    
    // 水平滚动条（如果需要）
    .os-scrollbar-horizontal {
      bottom: 2px;
      height: 8px;
      
      .os-scrollbar-track {
        background: transparent;
        border-radius: 4px;
        
        &:hover {
          background: var(--os-track-bg-hover);
        }
      }
      
      .os-scrollbar-handle {
        border-radius: 4px;
        min-width: 30px;
        background: var(--os-handle-bg);
        transition: all 0.2s ease;
        
        &:hover {
          background: var(--os-handle-bg-hover);
        }
        
        &:active {
          background: var(--os-handle-bg-active);
        }
      }
    }
  }
}

// 全局滚动条样式覆盖（作为备用方案）
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.2s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.35);
  }
  
  &:active {
    background: rgba(0, 0, 0, 0.5);
  }
}

// 深色模式下的 webkit 滚动条
html.dark {
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    
    &:hover {
      background: rgba(255, 255, 255, 0.45);
    }
    
    &:active {
      background: rgba(255, 255, 255, 0.6);
    }
  }
}

// Firefox 滚动条样式
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

html.dark * {
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}
