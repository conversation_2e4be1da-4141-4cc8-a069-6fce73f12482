<template>
  <div class="vehicle-list">
    <div class="vehicle-list-header">
      <h3>可用车辆</h3>
      <div class="header-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索车牌号或车辆编号"
          size="small"
          style="width: 200px"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          size="small"
          style="width: 120px"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option label="正常" value="1" />
          <el-option label="停用" value="0" />
          <el-option label="维修中" value="2" />
        </el-select>
        <el-button size="small" @click="refreshVehicles">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="vehicle-stats">
      <div class="stat-item">
        <span class="stat-label">总数:</span>
        <span class="stat-value">{{ vehicles.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">可用:</span>
        <span class="stat-value available">{{ availableCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已分配:</span>
        <span class="stat-value assigned">{{ assignedCount }}</span>
      </div>
    </div>

    <div class="vehicle-grid-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else-if="filteredVehicles.length === 0" class="empty-container">
        <el-empty description="暂无车辆数据" />
      </div>
      
      <div v-else class="vehicle-grid">
        <VehicleCard
          v-for="vehicle in filteredVehicles"
          :key="vehicle.vehicleId"
          :vehicle="vehicle"
          @drag-start="handleVehicleDragStart"
          @drag-end="handleVehicleDragEnd"
        />
      </div>
    </div>

    <!-- 拖拽提示 -->
    <div v-if="isDragging" class="drag-overlay">
      <div class="drag-hint">
        <el-icon><Rank /></el-icon>
        <span>将车辆拖拽到时间线上进行分配</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Search, Refresh, Rank } from '@element-plus/icons-vue'
import VehicleCard from './VehicleCard.vue'

// 接口定义
interface Vehicle {
  vehicleId: number | string
  plateNumber: string
  vehicleNumber: string
  vehicleType: string
  brandModel?: string
  seatCount?: number
  status: string
  isAssigned?: boolean
  assignedTime?: string
  totalMileage?: number
  remark?: string
}

// Props
interface Props {
  vehicles: Vehicle[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits
interface Emits {
  (e: 'vehicleDragStart', vehicle: Vehicle, event: DragEvent): void
  (e: 'vehicleDragEnd', vehicle: Vehicle, event: DragEvent): void
  (e: 'refresh'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref('')
const isDragging = ref(false)

// 计算属性
const filteredVehicles = computed(() => {
  let filtered = props.vehicles

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(vehicle =>
      vehicle.plateNumber.toLowerCase().includes(keyword) ||
      vehicle.vehicleNumber.toLowerCase().includes(keyword)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(vehicle => vehicle.status === statusFilter.value)
  }

  return filtered
})

const availableCount = computed(() => {
  return props.vehicles.filter(v => v.status === '1' && !v.isAssigned).length
})

const assignedCount = computed(() => {
  return props.vehicles.filter(v => v.isAssigned).length
})

// 方法
const handleVehicleDragStart = (vehicle: Vehicle, event: DragEvent) => {
  isDragging.value = true
  emit('vehicleDragStart', vehicle, event)
}

const handleVehicleDragEnd = (vehicle: Vehicle, event: DragEvent) => {
  isDragging.value = false
  emit('vehicleDragEnd', vehicle, event)
}

const refreshVehicles = () => {
  emit('refresh')
}

// 监听器
watch(() => props.vehicles, () => {
  // 当车辆数据变化时，可以执行一些操作
}, { deep: true })

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化操作
})

// 暴露方法给父组件
defineExpose({
  clearSearch: () => {
    searchKeyword.value = ''
    statusFilter.value = ''
  }
})
</script>

<style scoped>
.vehicle-list {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.vehicle-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.vehicle-list-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.vehicle-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-value.available {
  color: #67c23a;
}

.stat-value.assigned {
  color: #409eff;
}

.vehicle-grid-container {
  min-height: 300px;
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 10;
}

.drag-hint {
  background: #409eff;
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .vehicle-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .vehicle-list {
    padding: 16px;
  }
  
  .vehicle-list-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .vehicle-stats {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .vehicle-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .vehicle-grid {
    grid-template-columns: 1fr 1fr;
  }
}
</style>
