<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>数据展示</span>
          <div>
            <el-button type="primary" @click="handleScreen">大屏展示</el-button>
            <el-button type="info" @click="handleStatistics">统计报表</el-button>
            <el-button type="success" @click="handlePassenger">客流统计</el-button>
            <el-button type="warning" @click="handleRealtime">实时数据</el-button>
          </div>
        </div>
      </template>
      
      <div class="display-content">
        <el-alert
          title="数据展示中心"
          description="展示各类运营数据和统计信息。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="数据展示功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="Display">
const { proxy } = getCurrentInstance();

function handleScreen() {
  proxy.$router.push('/monitor/display/screen');
}

function handleStatistics() {
  proxy.$router.push('/monitor/display/statistics');
}

function handlePassenger() {
  proxy.$router.push('/monitor/display/passenger');
}

function handleRealtime() {
  proxy.$router.push('/monitor/display/realtime');
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.display-content {
  padding: 20px;
}
</style>
