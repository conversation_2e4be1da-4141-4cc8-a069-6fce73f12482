<template>
  <div class="time-selector">
    <div class="time-selector-header">
      <h3>发车时间选择</h3>
      <div class="header-actions">
        <el-dropdown @command="handleTemplateCommand">
          <el-button type="success" size="small">
            <el-icon><Document /></el-icon>
            选择模板
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="workday">工作日模板</el-dropdown-item>
              <el-dropdown-item command="weekend">周末模板</el-dropdown-item>
              <el-dropdown-item command="holiday">节假日模板</el-dropdown-item>
              <el-dropdown-item command="peak">高峰期模板</el-dropdown-item>
              <el-dropdown-item command="night">夜班模板</el-dropdown-item>
              <el-dropdown-item divided command="custom">自定义模板</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" size="small" @click="showAddTimeDialog = true">
          <el-icon><Plus /></el-icon>
          添加时间点
        </el-button>
      </div>
    </div>

    <div class="time-points-container">
      <div class="layout-controls" v-if="selectedTimes.length > 0">
        <el-radio-group v-model="layoutMode" size="small">
          <el-radio-button value="normal">常规排列</el-radio-button>
          <el-radio-button value="snake">蛇形排列</el-radio-button>
        </el-radio-group>
      </div>
      
      <div 
        class="time-points-grid" 
        :class="{ 
          'snake-layout': layoutMode === 'snake',
          'timeline-mode': layoutMode === 'snake' 
        }"
      >
        <!-- 蛇形时间线连接线 -->
        <svg 
          v-if="layoutMode === 'snake' && selectedTimes.length > 1" 
          class="timeline-connections"
          :viewBox="`0 0 ${gridWidth} ${gridHeight}`"
        >
          <path 
            :d="timelinePath" 
            stroke="#409eff" 
            stroke-width="3" 
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="timeline-path"
          />
          <!-- 时间流向箭头 -->
          <defs>
            <marker 
              id="arrowhead" 
              markerWidth="10" 
              markerHeight="7" 
              refX="9" 
              refY="3.5" 
              orient="auto"
            >
              <polygon 
                points="0 0, 10 3.5, 0 7" 
                fill="#409eff"
              />
            </marker>
          </defs>
          <path 
            :d="arrowPath" 
            stroke="#409eff" 
            stroke-width="2" 
            fill="none"
            marker-end="url(#arrowhead)"
            class="arrow-path"
          />
        </svg>

        <div
          v-for="(timeData, index) in displayTimes"
          :key="`${timeData.time}-${timeData.originalIndex}`"
          class="time-point-item"
          :class="{ 
            active: activeTimeIndex === timeData.originalIndex,
            'with-timeline': layoutMode === 'snake'
          }"
          :style="getTimePointStyle(index)"
          @click="selectTime(timeData.originalIndex)"
        >
          <div class="time-point-circle">
            <div class="time-display">{{ timeData.time }}</div>
            <div class="time-index">{{ index + 1 }}</div>
          </div>
          <el-button
            type="danger"
            size="small"
            circle
            class="remove-btn"
            @click.stop="removeTime(timeData.originalIndex)"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>

      <div v-if="selectedTimes.length === 0" class="empty-state">
        <el-empty description="请添加发车时间点" />
      </div>
      
      <!-- 蛇形时间线说明 -->
      <div v-if="layoutMode === 'snake' && selectedTimes.length > 1" class="snake-timeline-info">
        <el-icon class="info-icon"><InfoFilled /></el-icon>
        蛇形时间线显示发车时间的连续流向，按照时间顺序呈蛇形排列
      </div>
    </div>

    <!-- 添加时间对话框 -->
    <el-dialog
      v-model="showAddTimeDialog"
      title="添加发车时间"
      width="400px"
      :before-close="handleCloseDialog"
    >
      <el-form :model="timeForm" label-width="80px">
        <el-form-item label="时间">
          <el-time-picker
            v-model="timeForm.time"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="timeForm.remark"
            placeholder="可选，如：首班车、末班车"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="addTime">确定</el-button>
      </template>
    </el-dialog>

    <!-- 自定义模板对话框 -->
    <el-dialog
      v-model="showCustomTemplateDialog"
      title="自定义时间模板"
      width="600px"
      :before-close="handleCloseCustomTemplate"
    >
      <div class="custom-template-content">
        <el-form :model="customTemplateForm" label-width="100px">
          <el-form-item label="模板名称">
            <el-input
              v-model="customTemplateForm.name"
              placeholder="请输入模板名称"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="时间设置">
            <div class="time-range-settings">
              <div class="setting-row">
                <span class="setting-label">起始时间:</span>
                <el-time-picker
                  v-model="customTemplateForm.startTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  placeholder="起始时间"
                />
              </div>
              <div class="setting-row">
                <span class="setting-label">结束时间:</span>
                <el-time-picker
                  v-model="customTemplateForm.endTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  placeholder="结束时间"
                />
              </div>
              <div class="setting-row">
                <span class="setting-label">间隔时间:</span>
                <el-input-number
                  v-model="customTemplateForm.interval"
                  :min="5"
                  :max="120"
                  :step="5"
                  controls-position="right"
                />
                <span class="unit">分钟</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="预览">
            <div class="template-preview">
              <el-tag
                v-for="time in previewTimes"
                :key="time"
                size="small"
                class="preview-tag"
              >
                {{ time }}
              </el-tag>
              <div v-if="previewTimes.length === 0" class="preview-empty">
                请设置时间参数
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showCustomTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="applyCustomTemplate">应用模板</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close, Document, ArrowDown, InfoFilled } from '@element-plus/icons-vue'

// 接口定义
interface TimePoint {
  time: string
  remark?: string
}

// Props
interface Props {
  modelValue: string[]
  maxTimes?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxTimes: 50
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'timeSelected', index: number, time: string): void
  (e: 'timesChanged', times: string[]): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const selectedTimes = ref<string[]>([...props.modelValue])
const activeTimeIndex = ref<number>(-1)
const showAddTimeDialog = ref(false)
const showCustomTemplateDialog = ref(false)
const layoutMode = ref<'normal' | 'snake'>('normal')
const gridWidth = ref(600)
const gridHeight = ref(400)

const timeForm = reactive<TimePoint>({
  time: '',
  remark: ''
})

const customTemplateForm = reactive({
  name: '',
  startTime: '06:00',
  endTime: '22:00',
  interval: 30
})

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  // 只有当值真正不同时才更新，避免循环
  if (JSON.stringify(newVal) !== JSON.stringify(selectedTimes.value)) {
    selectedTimes.value = [...newVal]
  }
}, { deep: true })

// 监听selectedTimes变化，同步到父组件
watch(selectedTimes, (newTimes) => {
  // 只有当值真正不同时才emit，避免循环
  if (JSON.stringify(newTimes) !== JSON.stringify(props.modelValue)) {
    emit('update:modelValue', newTimes)
    emit('timesChanged', newTimes)
  }
}, { deep: true })

// 预设模板
const timeTemplates = {
  workday: {
    name: '工作日模板',
    times: ['06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00']
  },
  weekend: {
    name: '周末模板',
    times: ['07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00']
  },
  holiday: {
    name: '节假日模板',
    times: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00']
  },
  peak: {
    name: '高峰期模板',
    times: ['06:00', '06:15', '06:30', '06:45', '07:00', '07:15', '07:30', '07:45', '08:00', '08:15', '08:30', '17:00', '17:15', '17:30', '17:45', '18:00', '18:15', '18:30', '18:45', '19:00']
  },
  night: {
    name: '夜班模板',
    times: ['22:00', '22:30', '23:00', '23:30', '00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30', '04:00', '04:30', '05:00', '05:30']
  }
}

// 计算属性
const displayTimes = computed(() => {
  if (layoutMode.value === 'normal') {
    return selectedTimes.value.map((time, index) => ({
      time,
      originalIndex: index,
      position: { row: 0, col: index, x: 0, y: 0 }
    }))
  }
  
  // 蛇形排列逻辑 - 真正的蛇形路径
  const times = selectedTimes.value.map((time, index) => ({
    time,
    originalIndex: index,
    position: { row: 0, col: 0, x: 0, y: 0 }
  }))
  
  const result = []
  let cols = 6 // 默认每行6个
  
  // 响应式调整列数
  if (typeof window !== 'undefined') {
    if (window.innerWidth <= 480) {
      cols = 3
    } else if (window.innerWidth <= 768) {
      cols = 4
    }
  }
  
  const itemWidth = 100
  const itemHeight = 80
  const gapX = 20
  const gapY = 40
  
  for (let i = 0; i < times.length; i++) {
    const rowIndex = Math.floor(i / cols)
    const colIndex = i % cols
    
    let actualCol = colIndex
    let x, y
    
    // 蛇形路径：奇数行反向
    if (rowIndex % 2 === 1) {
      actualCol = cols - 1 - colIndex
    }
    
    x = actualCol * (itemWidth + gapX) + itemWidth / 2
    y = rowIndex * (itemHeight + gapY) + itemHeight / 2
    
    result.push({
      ...times[i],
      position: {
        row: rowIndex,
        col: actualCol,
        x,
        y
      }
    })
  }
  
  // 更新网格尺寸
  if (result.length > 0) {
    const maxRow = Math.max(...result.map(item => item.position.row))
    const maxCol = cols - 1
    gridWidth.value = (maxCol + 1) * (itemWidth + gapX)
    gridHeight.value = (maxRow + 1) * (itemHeight + gapY)
  }
  
  return result
})

// 时间线路径计算 - 优化的蛇形连接算法
const timelinePath = computed(() => {
  if (displayTimes.value.length < 2) return ''
  
  let path = ''
  const points = displayTimes.value.map(item => item.position)
  let cols = 6
  
  if (typeof window !== 'undefined') {
    if (window.innerWidth <= 480) {
      cols = 3
    } else if (window.innerWidth <= 768) {
      cols = 4
    }
  }
  
  for (let i = 0; i < points.length; i++) {
    const point = points[i]
    
    if (i === 0) {
      // 起始点
      path += `M ${point.x} ${point.y}`
    } else {
      const prevPoint = points[i - 1]
      const currentRow = Math.floor(i / cols)
      const prevRow = Math.floor((i - 1) / cols)
      
      if (currentRow === prevRow) {
        // 同一行，直线连接
        path += ` L ${point.x} ${point.y}`
      } else {
        // 换行了，需要蛇形转弯
        const prevRowIndex = prevRow
        const currentRowIndex = currentRow
        
        // 计算转弯点
        let turnX1, turnY1, turnX2, turnY2
        
        if (prevRowIndex % 2 === 0) {
          // 上一行是正向（左到右），转弯到右边
          turnX1 = (cols - 1) * 120 + 50 // 行末位置
          turnY1 = prevPoint.y
          turnX2 = turnX1
          turnY2 = point.y
        } else {
          // 上一行是反向（右到左），转弯到左边
          turnX1 = 50 // 行首位置
          turnY1 = prevPoint.y
          turnX2 = turnX1
          turnY2 = point.y
        }
        
        // 添加转弯路径
        path += ` L ${turnX1} ${turnY1}`  // 到行末/行首
        path += ` L ${turnX2} ${turnY2}`  // 向下
        path += ` L ${point.x} ${point.y}` // 到新位置
      }
    }
  }
  
  return path
})

// 箭头路径 - 在最后一个点添加方向箭头
const arrowPath = computed(() => {
  if (displayTimes.value.length < 2) return ''
  
  const lastPoint = displayTimes.value[displayTimes.value.length - 1].position
  const secondLastPoint = displayTimes.value[displayTimes.value.length - 2].position
  
  const dx = lastPoint.x - secondLastPoint.x
  const dy = lastPoint.y - secondLastPoint.y
  const length = Math.sqrt(dx * dx + dy * dy)
  
  if (length === 0) return ''
  
  const unitX = dx / length
  const unitY = dy / length
  const arrowLength = 20
  
  const startX = lastPoint.x - unitX * 25
  const startY = lastPoint.y - unitY * 25
  const endX = lastPoint.x - unitX * 5
  const endY = lastPoint.y - unitY * 5
  
  return `M ${startX} ${startY} L ${endX} ${endY}`
})

const previewTimes = computed(() => {
  if (!customTemplateForm.startTime || !customTemplateForm.endTime || !customTemplateForm.interval) {
    return []
  }

  const times: string[] = []
  const start = parseTime(customTemplateForm.startTime)
  const end = parseTime(customTemplateForm.endTime)
  const interval = customTemplateForm.interval

  let current = start
  while (current <= end) {
    times.push(formatTimeFromMinutes(current))
    current += interval
  }

  return times
})

// 方法
const getTimePointStyle = (index: number) => {
  if (layoutMode.value === 'normal') return {}
  
  const timeData = displayTimes.value[index]
  if (!timeData) return {}
  
  return {
    position: 'absolute',
    left: `${timeData.position.x - 50}px`,
    top: `${timeData.position.y - 40}px`,
    zIndex: 10
  }
}

const selectTime = (index: number) => {
  activeTimeIndex.value = index
  emit('timeSelected', index, selectedTimes.value[index])
}

const addTime = () => {
  if (!timeForm.time) {
    ElMessage.warning('请选择时间')
    return
  }

  // 检查时间是否已存在
  if (selectedTimes.value.includes(timeForm.time)) {
    ElMessage.warning('该时间点已存在')
    return
  }

  // 检查最大数量限制
  if (selectedTimes.value.length >= props.maxTimes) {
    ElMessage.warning(`最多只能添加${props.maxTimes}个时间点`)
    return
  }

  // 添加时间并排序
  selectedTimes.value.push(timeForm.time)
  selectedTimes.value.sort()

  // 重置表单
  timeForm.time = ''
  timeForm.remark = ''
  showAddTimeDialog.value = false

  ElMessage.success('时间点添加成功')
}

const removeTime = (index: number) => {
  selectedTimes.value.splice(index, 1)

  // 调整activeTimeIndex
  if (activeTimeIndex.value === index) {
    activeTimeIndex.value = -1
  } else if (activeTimeIndex.value > index) {
    activeTimeIndex.value--
  }

  ElMessage.success('时间点已删除')
}

const handleCloseDialog = () => {
  timeForm.time = ''
  timeForm.remark = ''
  showAddTimeDialog.value = false
}

// 模板相关方法
const handleTemplateCommand = async (command: string) => {
  if (command === 'custom') {
    showCustomTemplateDialog.value = true
    return
  }

  const template = timeTemplates[command as keyof typeof timeTemplates]
  if (!template) return

  if (selectedTimes.value.length > 0) {
    try {
      await ElMessageBox.confirm(
        `当前已有${selectedTimes.value.length}个时间点，应用"${template.name}"将替换现有时间点，是否继续？`,
        '确认应用模板',
        {
          type: 'warning',
          confirmButtonText: '替换',
          cancelButtonText: '取消'
        }
      )
    } catch {
      return // 用户取消
    }
  }

  selectedTimes.value = [...template.times]
  ElMessage.success(`已应用"${template.name}"，共${template.times.length}个时间点`)
}

const handleCloseCustomTemplate = () => {
  customTemplateForm.name = ''
  customTemplateForm.startTime = '06:00'
  customTemplateForm.endTime = '22:00'
  customTemplateForm.interval = 30
  showCustomTemplateDialog.value = false
}

const applyCustomTemplate = () => {
  if (!customTemplateForm.startTime || !customTemplateForm.endTime) {
    ElMessage.warning('请设置起始和结束时间')
    return
  }

  if (parseTime(customTemplateForm.startTime) >= parseTime(customTemplateForm.endTime)) {
    ElMessage.warning('结束时间必须大于起始时间')
    return
  }

  const times = previewTimes.value
  if (times.length === 0) {
    ElMessage.warning('生成的时间点为空，请检查设置')
    return
  }

  if (times.length > props.maxTimes) {
    ElMessage.warning(`生成的时间点数量(${times.length})超过最大限制(${props.maxTimes})`)
    return
  }

  selectedTimes.value = times
  showCustomTemplateDialog.value = false

  const templateName = customTemplateForm.name || '自定义模板'
  ElMessage.success(`已应用"${templateName}"，共${times.length}个时间点`)
}

// 时间处理工具函数
const parseTime = (timeStr: string): number => {
  const [hours, minutes] = timeStr.split(':').map(Number)
  return hours * 60 + minutes
}

const formatTimeFromMinutes = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

// 快速添加常用时间点
const addQuickTimes = () => {
  const quickTimes = ['06:00', '06:30', '07:00', '07:30', '08:00', '18:00', '18:30', '19:00', '22:00']
  quickTimes.forEach(time => {
    if (!selectedTimes.value.includes(time)) {
      selectedTimes.value.push(time)
    }
  })
  selectedTimes.value.sort()
}

// 暴露方法给父组件
defineExpose({
  addQuickTimes,
  clearTimes: () => {
    selectedTimes.value = []
    activeTimeIndex.value = -1
  }
})
</script>

<style scoped>
.time-selector {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.time-selector-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-points-container {
  min-height: 120px;
}

.layout-controls {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
}

.time-points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  position: relative;
}

/* 蛇形时间线布局 */
.time-points-grid.snake-layout {
  display: block;
  position: relative;
  min-height: 200px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 2px solid #e4e7ed;
}

/* 时间线连接线SVG */
.timeline-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.timeline-path {
  stroke-dasharray: 5, 5;
  animation: dash 2s linear infinite;
}

.arrow-path {
  stroke-dasharray: none;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* 时间点样式重写 */
.time-point-item.with-timeline {
  position: absolute;
  width: 100px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-point-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  position: relative;
  z-index: 2;
  border: 3px solid white;
}

.time-point-item.with-timeline:hover .time-point-circle {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.time-point-item.with-timeline.active .time-point-circle {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  transform: scale(1.15);
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.5);
}

.time-display {
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 2px;
}

.time-index {
  font-size: 10px;
  opacity: 0.8;
  font-weight: 500;
}

/* 删除按钮在时间线模式下的样式 */
.time-point-item.with-timeline .remove-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  min-height: 20px;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 3;
  background: #f56c6c;
  border: 2px solid white;
}

.time-point-item.with-timeline:hover .remove-btn {
  opacity: 1;
  transform: scale(1.1);
}

/* 常规模式保持原样 */
.time-point-item:not(.with-timeline) {
  position: relative;
  background: #f5f7fa;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.time-point-item:not(.with-timeline):hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.time-point-item:not(.with-timeline).active {
  border-color: #409eff;
  background: #409eff;
  color: white;
}

.time-point-item:not(.with-timeline) .time-display {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  color: inherit;
}

.time-point-item:not(.with-timeline) .remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  min-height: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.time-point-item:not(.with-timeline):hover .remove-btn {
  opacity: 1;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
}

/* 自定义模板对话框样式 */
.custom-template-content {
  padding: 8px 0;
}

.time-range-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.setting-label {
  min-width: 80px;
  font-size: 14px;
  color: #606266;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 8px;
}

.template-preview {
  min-height: 80px;
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.preview-tag {
  margin: 2px 4px 2px 0;
}

.preview-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  color: #c0c4cc;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .time-points-grid.snake-layout {
    padding: 15px;
    min-height: 150px;
  }

  .time-point-circle {
    width: 50px;
    height: 50px;
    border: 2px solid white;
  }

  .time-display {
    font-size: 11px;
  }

  .time-index {
    font-size: 9px;
  }

  .setting-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .setting-label {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .time-points-grid.snake-layout {
    padding: 10px;
    min-height: 120px;
  }
  
  .time-point-item.with-timeline {
    width: 80px;
    height: 60px;
  }
  
  .time-point-circle {
    width: 40px;
    height: 40px;
  }
  
  .time-display {
    font-size: 10px;
  }

  .time-index {
    font-size: 8px;
  }

  .time-point-item:not(.with-timeline) {
    padding: 8px;
    min-height: 60px;
  }
  
  .time-point-item:not(.with-timeline) .time-display {
    font-size: 14px;
  }
}

/* 蛇形时间线说明文字 */
.snake-timeline-info {
  margin-top: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 12px;
  color: #0369a1;
  text-align: center;
}

.snake-timeline-info .info-icon {
  margin-right: 4px;
  color: #0284c7;
}

/* 时间线模式的额外视觉提示 */
.time-points-grid.snake-layout::before {
  content: '时间流向 →';
  position: absolute;
  top: 5px;
  right: 10px;
  font-size: 12px;
  color: #409eff;
  font-weight: 600;
  opacity: 0.7;
}
</style>
