<template>
  <div class="test-page">
    <el-card class="welcome-card">
      <template #header>
        <div class="card-header">
          <span>🎉 新布局测试页面</span>
        </div>
      </template>
      
      <div class="content">
        <h2>顶部菜单布局已成功加载！</h2>
        <p>这是一个测试页面，用于验证新的顶部菜单布局是否正常工作。</p>
        
        <el-divider />
        
        <div class="feature-list">
          <h3>新布局特性：</h3>
          <ul>
            <li>✅ 顶部横向主菜单</li>
            <li>✅ 权限控制的菜单显示</li>
            <li>✅ 左侧智能操作面板</li>
            <li>✅ 面包屑导航</li>
            <li>✅ 标签页管理</li>
            <li>✅ 响应式设计</li>
          </ul>
        </div>
        
        <el-divider />
        
        <div class="actions">
          <el-button type="primary" @click="testNotification">
            测试通知
          </el-button>
          <el-button type="success" @click="testMessage">
            测试消息
          </el-button>
          <el-button type="warning" @click="togglePanel">
            切换操作面板
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElNotification } from 'element-plus'
import { useSettingsStore } from '@/store/modules/settings'

const settingsStore = useSettingsStore()

const testNotification = () => {
  ElNotification({
    title: '测试通知',
    message: '新布局通知系统工作正常！',
    type: 'success'
  })
}

const testMessage = () => {
  ElMessage.success('新布局消息系统工作正常！')
}

const togglePanel = () => {
  settingsStore.toggleActionPanel()
  ElMessage.info('操作面板已切换')
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
}

.welcome-card {
  max-width: 800px;
  margin: 0 auto;
  
  .card-header {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
  }
}

.content {
  text-align: center;
  
  h2 {
    color: #409eff;
    margin-bottom: 20px;
  }
  
  p {
    color: #666;
    margin-bottom: 30px;
  }
}

.feature-list {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  
  h3 {
    margin-bottom: 15px;
    color: #333;
  }
  
  ul {
    list-style: none;
    padding: 0;
    
    li {
      padding: 5px 0;
      color: #555;
    }
  }
}

.actions {
  margin-top: 30px;
  
  .el-button {
    margin: 0 10px;
  }
}
</style>