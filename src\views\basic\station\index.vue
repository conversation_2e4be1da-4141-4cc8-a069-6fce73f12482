<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="线路" prop="routeId">
        <el-select v-model="queryParams.routeId" placeholder="请选择线路" clearable>
          <el-option
            v-for="route in routeOptions"
            :key="route.routeId"
            :label="route.routeName"
            :value="route.routeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="站点名称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入站点名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="handleSort">排序</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="站点ID" align="center" prop="stationId" />
      <el-table-column label="线路" align="center" prop="routeName" />
      <el-table-column label="站点名称" align="center" prop="stationName" />
      <el-table-column label="站点序号" align="center" prop="stationOrder" />
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" />
      <el-table-column label="站台类型" align="center" prop="platformType">
        <template #default="scope">
          <el-tag v-if="scope.row.platformType === 'island'" type="success">岛式站台</el-tag>
          <el-tag v-else-if="scope.row.platformType === 'side'" type="info">侧式站台</el-tag>
          <el-tag v-else type="warning">港湾式站台</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否有候车亭" align="center" prop="hasShelter">
        <template #default="scope">
          <el-tag v-if="scope.row.hasShelter" type="success">有</el-tag>
          <el-tag v-else type="danger">无</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '1'" type="success">正常</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Position" @click="handleLocation(scope.row)">定位</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改站点对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="stationRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路" prop="routeId">
              <el-select v-model="form.routeId" placeholder="请选择线路" @change="handleRouteChange">
                <el-option
                  v-for="route in routeOptions"
                  :key="route.routeId"
                  :label="route.routeName"
                  :value="route.routeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点名称" prop="stationName">
              <el-input v-model="form.stationName" placeholder="请输入站点名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="站点序号" prop="stationOrder">
              <el-input-number v-model="form.stationOrder" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站台类型" prop="platformType">
              <el-select v-model="form.platformType" placeholder="请选择站台类型">
                <el-option label="岛式站台" value="island" />
                <el-option label="侧式站台" value="side" />
                <el-option label="港湾式站台" value="bay" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number v-model="form.longitude" :precision="6" :min="-180" :max="180" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number v-model="form.latitude" :precision="6" :min="-90" :max="90" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否有候车亭" prop="hasShelter">
              <el-switch v-model="form.hasShelter" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="正常" value="1" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="站点地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入站点地址" />
        </el-form-item>
        <el-form-item label="周边设施" prop="facilities">
          <el-input v-model="form.facilities" type="textarea" :rows="2" placeholder="请输入周边设施信息" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 站点排序对话框 -->
    <el-dialog title="站点排序" v-model="sortOpen" width="600px" append-to-body>
      <el-alert
        title="拖拽站点可调整顺序"
        type="info"
        :closable="false"
        style="margin-bottom: 15px;"
      />
      <div class="sort-list">
        <div
          v-for="(station, index) in sortList"
          :key="station.stationId"
          class="sort-item"
          draggable="true"
          @dragstart="handleDragStart(index)"
          @dragover.prevent
          @drop="handleDrop(index)"
        >
          <el-icon class="drag-handle"><Rank /></el-icon>
          <span class="station-order">{{ index + 1 }}</span>
          <span class="station-name">{{ station.stationName }}</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSort">确 定</el-button>
          <el-button @click="sortOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Station">
const { proxy } = getCurrentInstance();

const stationList = ref([]);
const routeOptions = ref([]);
const sortList = ref([]);
const open = ref(false);
const sortOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dragIndex = ref(-1);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    routeId: null,
    stationName: null,
    status: null
  },
  rules: {
    routeId: [
      { required: true, message: "请选择线路", trigger: "change" }
    ],
    stationName: [
      { required: true, message: "站点名称不能为空", trigger: "blur" }
    ],
    stationOrder: [
      { required: true, message: "站点序号不能为空", trigger: "blur" }
    ],
    longitude: [
      { required: true, message: "经度不能为空", trigger: "blur" }
    ],
    latitude: [
      { required: true, message: "纬度不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询站点列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      stationId: 1,
      routeId: 1,
      routeName: "1路",
      stationName: "火车站",
      stationOrder: 1,
      longitude: 116.407526,
      latitude: 39.904030,
      platformType: "island",
      hasShelter: true,
      address: "北京市东城区火车站广场",
      facilities: "地铁换乘、商场、餐厅",
      status: "1",
      remark: "起点站"
    },
    {
      stationId: 2,
      routeId: 1,
      routeName: "1路",
      stationName: "市政府",
      stationOrder: 2,
      longitude: 116.408526,
      latitude: 39.905030,
      platformType: "side",
      hasShelter: true,
      address: "北京市东城区市政府门前",
      facilities: "政务大厅、银行",
      status: "1",
      remark: "重要站点"
    },
    {
      stationId: 3,
      routeId: 1,
      routeName: "1路",
      stationName: "体育中心",
      stationOrder: 25,
      longitude: 116.409526,
      latitude: 39.906030,
      platformType: "bay",
      hasShelter: false,
      address: "北京市朝阳区体育中心",
      facilities: "体育场馆、停车场",
      status: "1",
      remark: "终点站"
    }
  ];
  
  setTimeout(() => {
    stationList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

/** 获取线路选项 */
function getRouteOptions() {
  routeOptions.value = [
    { routeId: 1, routeName: "1路" },
    { routeId: 2, routeName: "2路" },
    { routeId: 3, routeName: "3路" }
  ];
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.stationId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getRouteOptions();
  open.value = true;
  title.value = "添加站点";
}

function handleUpdate(row) {
  reset();
  getRouteOptions();
  form.value = { ...row };
  open.value = true;
  title.value = "修改站点";
}

function handleDelete(row) {
  const stationIds = row.stationId || ids.value;
  proxy.$modal.confirm('是否确认删除站点编号为"' + stationIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleLocation(row) {
  proxy.$modal.msgInfo(`站点位置：经度 ${row.longitude}，纬度 ${row.latitude}`);
}

function handleSort() {
  sortList.value = [...stationList.value].sort((a, b) => a.stationOrder - b.stationOrder);
  sortOpen.value = true;
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有站点数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function handleRouteChange(routeId) {
  const route = routeOptions.value.find(r => r.routeId === routeId);
  if (route) {
    form.value.routeName = route.routeName;
  }
}

function handleDragStart(index) {
  dragIndex.value = index;
}

function handleDrop(index) {
  if (dragIndex.value !== -1 && dragIndex.value !== index) {
    const dragItem = sortList.value[dragIndex.value];
    sortList.value.splice(dragIndex.value, 1);
    sortList.value.splice(index, 0, dragItem);
  }
  dragIndex.value = -1;
}

function submitSort() {
  // 更新站点顺序
  sortList.value.forEach((station, index) => {
    station.stationOrder = index + 1;
  });
  proxy.$modal.msgSuccess("站点排序更新成功");
  sortOpen.value = false;
  getList();
}

function submitForm() {
  proxy.$refs["stationRef"].validate(valid => {
    if (valid) {
      if (form.value.stationId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    stationId: null,
    routeId: null,
    routeName: null,
    stationName: null,
    stationOrder: null,
    longitude: null,
    latitude: null,
    platformType: null,
    hasShelter: false,
    address: null,
    facilities: null,
    status: "1",
    remark: null
  };
  proxy.resetForm("stationRef");
}

onMounted(() => {
  getRouteOptions();
  getList();
});
</script>

<style scoped>
.sort-list {
  max-height: 400px;
  overflow-y: auto;
}

.sort-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 5px;
  background: #f5f7fa;
  border-radius: 4px;
  cursor: move;
  transition: background-color 0.3s;
}

.sort-item:hover {
  background: #e6f7ff;
}

.drag-handle {
  margin-right: 10px;
  color: #909399;
}

.station-order {
  display: inline-block;
  width: 30px;
  text-align: center;
  font-weight: bold;
  color: #409eff;
}

.station-name {
  flex: 1;
  margin-left: 10px;
}
</style>
