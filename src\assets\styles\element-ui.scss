.el-collapse {
  .collapse__title {
    font-weight: 600;
    padding: 0 8px;
    font-size: 1.2em;
    line-height: 1.1em;
  }
  .el-collapse-item__content {
    padding: 0 8px;
  }
}

.el-divider--horizontal {
  margin-bottom: 10px;
  margin-top: 10px;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

/*-------------Dialog-------------**/
.el-overlay {
  overflow: hidden;

  .el-overlay-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .el-dialog {
      margin: 0 auto !important;

      .el-dialog__body {
        padding: 15px !important;
      }
      .el-dialog__header {
        padding: 16px 16px 8px 16px;
        box-sizing: border-box;
        border-bottom: 1px solid var(--brder-color);
        margin-right: 0;
      }
    }
  }
}

.el-dialog__body {
  max-height: calc(90vh - 111px) !important;
  overflow-y: auto;
  overflow-x: hidden;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

/* 当 el-form 的 inline 属性为 true 时 */
/* 设置 label 的宽度默认为 68px */
.el-form--inline .el-form-item__label {
  width: 68px;
}

/* 设置 el-select 的宽度默认为 240px */
.el-form--inline .el-select {
  width: 240px;
}

/* 设置 el-input 的宽度默认为 240px */
.el-form--inline .el-input {
  width: 240px;
}

/* 设置 el-message-box 消息弹框内容强制换行 */
.el-message-box .el-message-box__message {
  word-break: break-word;
}

/* 车辆详情弹窗深色主题样式 - 全局覆盖 */
.el-dialog.vehicle-detail-dialog {
  background: linear-gradient(145deg, #1e293b 0%, #334155 50%, #1e293b 100%) !important;
  border: 2px solid rgba(59, 130, 246, 0.4) !important;
  border-radius: 16px !important;
  color: #e2e8f0 !important;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  margin-top: 5vh !important;
  max-height: 85vh !important;
}

.el-dialog.vehicle-detail-dialog .el-dialog__header {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(51, 65, 85, 0.95) 50%,
    rgba(30, 41, 59, 0.95) 100%) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3) !important;
  color: #e2e8f0 !important;
}

.el-dialog.vehicle-detail-dialog .el-dialog__title {
  color: #e2e8f0 !important;
  font-weight: 600;
}

.el-dialog.vehicle-detail-dialog .el-dialog__body {
  background: transparent !important;
  color: #e2e8f0 !important;
}

.el-dialog.vehicle-detail-dialog .el-dialog__footer {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(51, 65, 85, 0.95) 100%) !important;
  border-top: 2px solid rgba(59, 130, 246, 0.3) !important;
}

/* 弹窗遮罩层样式 */
.el-overlay.tech-modal {
  background: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(8px) !important;
}
