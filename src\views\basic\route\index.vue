<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="线路名称" prop="routeName">
        <el-input
          v-model="queryParams.routeName"
          placeholder="请输入线路名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="线路编号" prop="routeNumber">
        <el-input
          v-model="queryParams.routeNumber"
          placeholder="请输入线路编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="运营中" value="1" />
          <el-option label="停运" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Position" @click="handleStationManage">站点管理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="routeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="线路ID" align="center" prop="routeId" />
      <el-table-column label="线路编号" align="center" prop="routeNumber" />
      <el-table-column label="线路名称" align="center" prop="routeName" />
      <el-table-column label="起点站" align="center" prop="startStation" />
      <el-table-column label="终点站" align="center" prop="endStation" />
      <el-table-column label="线路长度(km)" align="center" prop="routeLength" />
      <el-table-column label="站点数量" align="center" prop="stationCount" />
      <el-table-column label="单程时间(分钟)" align="center" prop="singleTripTime" />
      <el-table-column label="票价(元)" align="center" prop="ticketPrice" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '1'" type="success">运营中</el-tag>
          <el-tag v-else type="danger">停运</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Position" @click="handleStationManage(scope.row)">站点</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改线路对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="routeRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路编号" prop="routeNumber">
              <el-input v-model="form.routeNumber" placeholder="请输入线路编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="线路名称" prop="routeName">
              <el-input v-model="form.routeName" placeholder="请输入线路名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="起点站" prop="startStation">
              <el-input v-model="form.startStation" placeholder="请输入起点站" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终点站" prop="endStation">
              <el-input v-model="form.endStation" placeholder="请输入终点站" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路长度(km)" prop="routeLength">
              <el-input-number v-model="form.routeLength" :precision="1" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单程时间(分钟)" prop="singleTripTime">
              <el-input-number v-model="form.singleTripTime" :min="1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="票价(元)" prop="ticketPrice">
              <el-input-number v-model="form.ticketPrice" :precision="2" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="运营中" value="1" />
                <el-option label="停运" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="线路描述" prop="routeDescription">
          <el-input v-model="form.routeDescription" type="textarea" :rows="3" placeholder="请输入线路描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Route">
const { proxy } = getCurrentInstance();

const routeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    routeName: null,
    routeNumber: null,
    status: null
  },
  rules: {
    routeNumber: [
      { required: true, message: "线路编号不能为空", trigger: "blur" }
    ],
    routeName: [
      { required: true, message: "线路名称不能为空", trigger: "blur" }
    ],
    startStation: [
      { required: true, message: "起点站不能为空", trigger: "blur" }
    ],
    endStation: [
      { required: true, message: "终点站不能为空", trigger: "blur" }
    ],
    routeLength: [
      { required: true, message: "线路长度不能为空", trigger: "blur" }
    ],
    singleTripTime: [
      { required: true, message: "单程时间不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询线路列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      routeId: 1,
      routeNumber: "001",
      routeName: "1路",
      startStation: "火车站",
      endStation: "体育中心",
      routeLength: 18.5,
      stationCount: 25,
      singleTripTime: 45,
      ticketPrice: 2.00,
      status: "1",
      routeDescription: "连接火车站与体育中心的主要线路",
      remark: "主干线路"
    },
    {
      routeId: 2,
      routeNumber: "002",
      routeName: "2路",
      startStation: "汽车站",
      endStation: "机场",
      routeLength: 22.3,
      stationCount: 18,
      singleTripTime: 55,
      ticketPrice: 3.00,
      status: "1",
      routeDescription: "连接汽车站与机场的快速线路",
      remark: "机场专线"
    },
    {
      routeId: 3,
      routeNumber: "003",
      routeName: "3路",
      startStation: "市中心",
      endStation: "工业园区",
      routeLength: 15.8,
      stationCount: 20,
      singleTripTime: 40,
      ticketPrice: 2.00,
      status: "0",
      routeDescription: "连接市中心与工业园区",
      remark: "临时停运维护"
    }
  ];
  
  setTimeout(() => {
    routeList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.routeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加线路";
}

function handleUpdate(row) {
  reset();
  form.value = { ...row };
  open.value = true;
  title.value = "修改线路";
}

function handleDelete(row) {
  const routeIds = row.routeId || ids.value;
  proxy.$modal.confirm('是否确认删除线路编号为"' + routeIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleStationManage(row) {
  if (row) {
    proxy.$router.push({
      path: '/basic/station',
      query: { routeId: row.routeId, routeName: row.routeName }
    });
  } else {
    proxy.$router.push('/basic/station');
  }
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有线路数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function submitForm() {
  proxy.$refs["routeRef"].validate(valid => {
    if (valid) {
      if (form.value.routeId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    routeId: null,
    routeNumber: null,
    routeName: null,
    startStation: null,
    endStation: null,
    routeLength: null,
    stationCount: 0,
    singleTripTime: null,
    ticketPrice: null,
    status: "1",
    routeDescription: null,
    remark: null
  };
  proxy.resetForm("routeRef");
}

getList();
</script>
