<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>实时位置跟踪</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>
      
      <div class="location-content">
        <el-alert
          title="实时位置跟踪"
          description="显示所有运营车辆的实时GPS位置信息。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="实时位置跟踪功能开发中，需要集成地图组件" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="VehicleLocation">
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-content {
  padding: 20px;
}
</style>
