<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="司机姓名" prop="driverName">
        <el-input
          v-model="queryParams.driverName"
          placeholder="请输入司机姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工号" prop="employeeId">
        <el-input
          v-model="queryParams.employeeId"
          placeholder="请输入工号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="在职" value="1" />
          <el-option label="离职" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="driverList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="司机ID" align="center" prop="driverId" />
      <el-table-column label="工号" align="center" prop="employeeId" />
      <el-table-column label="司机姓名" align="center" prop="driverName" />
      <el-table-column label="性别" align="center" prop="gender">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.gender"/>
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center" prop="age" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="驾驶证号" align="center" prop="licenseNumber" />
      <el-table-column label="准驾车型" align="center" prop="licenseType" />
      <el-table-column label="入职时间" align="center" prop="hireDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.hireDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改司机对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="driverRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="工号" prop="employeeId">
              <el-input v-model="form.employeeId" placeholder="请输入工号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="司机姓名" prop="driverName">
              <el-input v-model="form.driverName" placeholder="请输入司机姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择性别">
                <el-option
                  v-for="dict in sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄" prop="age">
              <el-input-number v-model="form.age" :min="18" :max="65" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="驾驶证号" prop="licenseNumber">
              <el-input v-model="form.licenseNumber" placeholder="请输入驾驶证号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="准驾车型" prop="licenseType">
              <el-select v-model="form.licenseType" placeholder="请选择准驾车型">
                <el-option label="A1" value="A1" />
                <el-option label="A2" value="A2" />
                <el-option label="A3" value="A3" />
                <el-option label="B1" value="B1" />
                <el-option label="B2" value="B2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入职时间" prop="hireDate">
              <el-date-picker
                v-model="form.hireDate"
                type="date"
                placeholder="选择入职时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Driver">
import { listDriver, getDriver, delDriver, addDriver, updateDriver } from "@/api/basic/driver";

const { proxy } = getCurrentInstance();
const { sys_user_sex, sys_normal_disable } = proxy.useDict('sys_user_sex', 'sys_normal_disable');

const driverList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    driverName: null,
    employeeId: null,
    status: null
  },
  rules: {
    employeeId: [
      { required: true, message: "工号不能为空", trigger: "blur" }
    ],
    driverName: [
      { required: true, message: "司机姓名不能为空", trigger: "blur" }
    ],
    phone: [
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ],
    idCard: [
      { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号码", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询司机列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      driverId: 1,
      employeeId: "D001",
      driverName: "张三",
      gender: "1",
      age: 35,
      phone: "13800138001",
      idCard: "110101198801011234",
      licenseNumber: "110101198801011234",
      licenseType: "A1",
      hireDate: "2020-01-15",
      status: "1",
      remark: "经验丰富的老司机"
    },
    {
      driverId: 2,
      employeeId: "D002",
      driverName: "李四",
      gender: "1",
      age: 28,
      phone: "13800138002",
      idCard: "110101199201011234",
      licenseNumber: "110101199201011234",
      licenseType: "A3",
      hireDate: "2021-03-20",
      status: "1",
      remark: "新入职司机"
    },
    {
      driverId: 3,
      employeeId: "D003",
      driverName: "王五",
      gender: "1",
      age: 42,
      phone: "13800138003",
      idCard: "110101198001011234",
      licenseNumber: "110101198001011234",
      licenseType: "A1",
      hireDate: "2018-06-10",
      status: "0",
      remark: "已离职"
    }
  ];

  setTimeout(() => {
    driverList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 其他方法实现...
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.driverId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加司机";
}

function handleUpdate(row) {
  reset();
  const driverId = row.driverId || ids.value;
  form.value = { ...row };
  open.value = true;
  title.value = "修改司机";
}

function handleDelete(row) {
  const driverIds = row.driverId || ids.value;
  proxy.$modal.confirm('是否确认删除司机编号为"' + driverIds + '"的数据项？').then(function() {
    // 模拟删除
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有司机数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function submitForm() {
  proxy.$refs["driverRef"].validate(valid => {
    if (valid) {
      if (form.value.driverId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    driverId: null,
    employeeId: null,
    driverName: null,
    gender: null,
    age: null,
    phone: null,
    idCard: null,
    licenseNumber: null,
    licenseType: null,
    hireDate: null,
    status: "1",
    remark: null
  };
  //proxy.resetForm("driverRef");
}

getList();
</script>
