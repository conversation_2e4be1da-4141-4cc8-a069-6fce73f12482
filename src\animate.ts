// 前缀
const animatePrefix = 'animate__animated ';
// 公交调度平台专用 - 简洁专业的页面切换动画
const pageTransitionAnimate = animatePrefix + 'animate__fadeIn';
// 备用动画选项（保持简洁风格）
const alternativeAnimates = [
  animatePrefix + 'animate__fadeIn',
  animatePrefix + 'animate__fadeInUp'
];
// 默认页面切换动画
const defaultAnimate = pageTransitionAnimate;
// 搜索隐藏显示动画
const searchAnimate = {
  enter: '',
  leave: ''
};

// 菜单搜索动画
const menuSearchAnimate = {
  enter: animatePrefix + 'animate__fadeIn',
  leave: animatePrefix + 'animate__fadeOut'
};
// logo动画
const logoAnimate = {
  enter: animatePrefix + 'animate__fadeIn',
  leave: animatePrefix + 'animate__fadeOut'
};

export default {
  pageTransitionAnimate,
  alternativeAnimates,
  defaultAnimate,
  searchAnimate,
  menuSearchAnimate,
  logoAnimate
};
