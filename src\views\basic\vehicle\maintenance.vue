<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNumber">
        <el-input
          v-model="queryParams.plateNumber"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维修日期" prop="maintenanceDate">
        <el-date-picker
          v-model="queryParams.maintenanceDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="类型" prop="maintenanceType">
        <el-select v-model="queryParams.maintenanceType" placeholder="请选择类型" clearable>
          <el-option label="保养" value="maintenance" />
          <el-option label="维修" value="repair" />
          <el-option label="年检" value="inspection" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="info" plain icon="Back" @click="handleBack">返回</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="maintenanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="maintenanceId" />
      <el-table-column label="车牌号" align="center" prop="plateNumber" />
      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" />
      <el-table-column label="维修日期" align="center" prop="maintenanceDate" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.maintenanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="maintenanceType">
        <template #default="scope">
          <el-tag v-if="scope.row.maintenanceType === 'maintenance'" type="success">保养</el-tag>
          <el-tag v-else-if="scope.row.maintenanceType === 'repair'" type="warning">维修</el-tag>
          <el-tag v-else type="info">年检</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="维修项目" align="center" prop="maintenanceItem" show-overflow-tooltip />
      <el-table-column label="费用(元)" align="center" prop="cost" />
      <el-table-column label="维修厂" align="center" prop="repairShop" show-overflow-tooltip />
      <el-table-column label="里程数(km)" align="center" prop="mileage" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 'completed'" type="success">已完成</el-tag>
          <el-tag v-else-if="scope.row.status === 'in_progress'" type="warning">进行中</el-tag>
          <el-tag v-else type="info">待处理</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改维修保养记录对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="maintenanceRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车辆" prop="vehicleId">
              <el-select v-model="form.vehicleId" placeholder="请选择车辆" @change="handleVehicleChange">
                <el-option
                  v-for="vehicle in vehicleOptions"
                  :key="vehicle.vehicleId"
                  :label="`${vehicle.plateNumber}(${vehicle.vehicleNumber})`"
                  :value="vehicle.vehicleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修日期" prop="maintenanceDate">
              <el-date-picker
                v-model="form.maintenanceDate"
                type="date"
                placeholder="选择维修日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型" prop="maintenanceType">
              <el-select v-model="form.maintenanceType" placeholder="请选择类型">
                <el-option label="保养" value="maintenance" />
                <el-option label="维修" value="repair" />
                <el-option label="年检" value="inspection" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="待处理" value="pending" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="费用(元)" prop="cost">
              <el-input-number v-model="form.cost" :precision="2" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前里程(km)" prop="mileage">
              <el-input-number v-model="form.mileage" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="维修项目" prop="maintenanceItem">
          <el-input v-model="form.maintenanceItem" placeholder="请输入维修项目" />
        </el-form-item>
        <el-form-item label="维修厂" prop="repairShop">
          <el-input v-model="form.repairShop" placeholder="请输入维修厂名称" />
        </el-form-item>
        <el-form-item label="维修内容" prop="maintenanceContent">
          <el-input v-model="form.maintenanceContent" type="textarea" :rows="3" placeholder="请输入详细维修内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="维修保养详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
        <el-descriptions-item label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
        <el-descriptions-item label="维修日期">{{ detailData.maintenanceDate }}</el-descriptions-item>
        <el-descriptions-item label="类型">
          <el-tag v-if="detailData.maintenanceType === 'maintenance'" type="success">保养</el-tag>
          <el-tag v-else-if="detailData.maintenanceType === 'repair'" type="warning">维修</el-tag>
          <el-tag v-else type="info">年检</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="维修项目" :span="2">{{ detailData.maintenanceItem }}</el-descriptions-item>
        <el-descriptions-item label="费用">{{ detailData.cost }} 元</el-descriptions-item>
        <el-descriptions-item label="里程数">{{ detailData.mileage }} km</el-descriptions-item>
        <el-descriptions-item label="维修厂" :span="2">{{ detailData.repairShop }}</el-descriptions-item>
        <el-descriptions-item label="状态" :span="2">
          <el-tag v-if="detailData.status === 'completed'" type="success">已完成</el-tag>
          <el-tag v-else-if="detailData.status === 'in_progress'" type="warning">进行中</el-tag>
          <el-tag v-else type="info">待处理</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="维修内容" :span="2">{{ detailData.maintenanceContent }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailData.remark }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VehicleMaintenance">
const { proxy } = getCurrentInstance();

const maintenanceList = ref([]);
const vehicleOptions = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const detailData = ref({});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNumber: null,
    maintenanceDate: null,
    maintenanceType: null
  },
  rules: {
    vehicleId: [
      { required: true, message: "请选择车辆", trigger: "change" }
    ],
    maintenanceDate: [
      { required: true, message: "请选择维修日期", trigger: "change" }
    ],
    maintenanceType: [
      { required: true, message: "请选择类型", trigger: "change" }
    ],
    maintenanceItem: [
      { required: true, message: "请输入维修项目", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询维修保养记录列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      maintenanceId: 1,
      vehicleId: 1,
      plateNumber: "京A12345",
      vehicleNumber: "V001",
      maintenanceDate: "2024-01-10",
      maintenanceType: "maintenance",
      maintenanceItem: "定期保养",
      maintenanceContent: "更换机油、机滤、空滤，检查刹车系统",
      cost: 850.00,
      repairShop: "宇通汽车服务站",
      mileage: 125000,
      status: "completed",
      remark: "定期保养，车况良好"
    },
    {
      maintenanceId: 2,
      vehicleId: 2,
      plateNumber: "京A12346",
      vehicleNumber: "V002",
      maintenanceDate: "2024-01-12",
      maintenanceType: "repair",
      maintenanceItem: "空调系统维修",
      maintenanceContent: "更换空调压缩机，添加制冷剂",
      cost: 1200.00,
      repairShop: "比亚迪授权维修站",
      mileage: 85000,
      status: "completed",
      remark: "空调系统故障已修复"
    },
    {
      maintenanceId: 3,
      vehicleId: 3,
      plateNumber: "京A12347",
      vehicleNumber: "V003",
      maintenanceDate: "2024-01-15",
      maintenanceType: "inspection",
      maintenanceItem: "年度检验",
      maintenanceContent: "车辆年检，包括安全性能检测、尾气检测等",
      cost: 300.00,
      repairShop: "市车辆检测中心",
      mileage: 180000,
      status: "in_progress",
      remark: "年检进行中"
    }
  ];

  setTimeout(() => {
    maintenanceList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

/** 获取车辆选项 */
function getVehicleOptions() {
  vehicleOptions.value = [
    { vehicleId: 1, plateNumber: "京A12345", vehicleNumber: "V001" },
    { vehicleId: 2, plateNumber: "京A12346", vehicleNumber: "V002" },
    { vehicleId: 3, plateNumber: "京A12347", vehicleNumber: "V003" }
  ];
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.maintenanceId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getVehicleOptions();
  open.value = true;
  title.value = "添加维修保养记录";
}

function handleUpdate(row) {
  reset();
  getVehicleOptions();
  form.value = { ...row };
  open.value = true;
  title.value = "修改维修保养记录";
}

function handleDetail(row) {
  detailData.value = { ...row };
  detailOpen.value = true;
}

function handleDelete(row) {
  const maintenanceIds = row.maintenanceId || ids.value;
  proxy.$modal.confirm('是否确认删除维修保养记录编号为"' + maintenanceIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleBack() {
  proxy.$router.push('/basic/vehicle');
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有维修保养记录数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function handleVehicleChange(vehicleId) {
  const vehicle = vehicleOptions.value.find(v => v.vehicleId === vehicleId);
  if (vehicle) {
    form.value.plateNumber = vehicle.plateNumber;
    form.value.vehicleNumber = vehicle.vehicleNumber;
  }
}

function submitForm() {
  proxy.$refs["maintenanceRef"].validate(valid => {
    if (valid) {
      if (form.value.maintenanceId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    maintenanceId: null,
    vehicleId: null,
    plateNumber: null,
    vehicleNumber: null,
    maintenanceDate: null,
    maintenanceType: null,
    maintenanceItem: null,
    maintenanceContent: null,
    cost: null,
    repairShop: null,
    mileage: null,
    status: "pending",
    remark: null
  };
  proxy.resetForm("maintenanceRef");
}

getList();
</script>
