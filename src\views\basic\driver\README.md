# 司机管理页面功能说明

## 新增功能

### 1. 组织机构树（左侧）
- **位置**: 页面左侧，占用4列宽度
- **功能**: 
  - 显示组织机构树形结构
  - 支持搜索部门名称
  - 点击部门节点可筛选该部门下的司机
  - 默认展开所有节点

### 2. 司机表单中的部门选择器
- **位置**: 新增/修改司机对话框中
- **功能**:
  - 使用 `el-tree-select` 组件
  - 支持树形结构选择部门
  - 必填字段验证

### 3. 数据结构更新
- **司机表格**: 新增"所属部门"列
- **API接口**: 
  - `DriverVO` 接口新增 `deptId` 和 `deptName` 字段
  - `DriverQuery` 接口新增 `deptId` 筛选参数

## 技术实现

### 组件引用
```javascript
import { treeselect } from "@/api/system/dept";
```

### 数据结构
```javascript
// 部门树数据
const deptOptions = ref([]);
const deptName = ref('');
const deptTreeRef = ref();

// 查询参数新增部门ID
queryParams: {
  // ... 其他参数
  deptId: null
}
```

### 主要方法
1. `getTreeSelect()` - 获取部门树数据
2. `filterNode()` - 部门树搜索过滤
3. `handleNodeClick()` - 部门节点点击事件
4. 数据筛选逻辑 - 支持按部门筛选司机列表

## 模拟数据
包含3个司机，分别属于不同部门：
- 张三 - 第一车队
- 李四 - 第二车队  
- 王五 - 机修组

## 使用说明
1. 页面加载时自动获取部门树和司机列表
2. 点击左侧部门树节点可筛选对应部门的司机
3. 新增/修改司机时可选择所属部门
4. 支持部门名称搜索功能
