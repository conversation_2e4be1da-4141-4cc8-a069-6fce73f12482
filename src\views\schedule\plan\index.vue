<template>
  <div class="route-schedule-container">


    <!-- 线路选择与车辆管理区域 - 合并卡片 -->
    <div class="route-and-vehicles-card">
      <!-- 线路选择器 -->
      <div class="route-selector-section">
        <div class="selector-header">
          <div class="selector-left">
            <div class="selector-label">
              <el-icon><Position /></el-icon>
              选择公交线路：
            </div>
            <el-select
              v-model="selectedRouteId"
              placeholder="请选择要排班的线路"
              size="default"
              style="width: 300px"
              @change="handleRouteChange"
              :loading="routesLoading"
            >
              <el-option
                v-for="route in routes"
                :key="route.routeId"
                :label="`${route.routeName} (${route.routeCode})`"
                :value="route.routeId"
              />
            </el-select>
          </div>

          <!-- {{ AURA: Modify - 线路信息单行显示，与下拉框高度一致 }} -->
          <div v-if="selectedRoute" class="route-info-inline">
            <span class="route-name">{{ selectedRoute.routeName }}</span>
            <span class="route-code">{{ selectedRoute.routeCode }}</span>
            <span class="route-stats">{{ selectedRoute.distance }}km · {{ selectedRoute.stationCount }}站</span>
            <el-tag size="small" type="success">{{ routeVehicles.length }}辆车</el-tag>
            <el-tag size="small" type="primary">{{ routeTimePoints.length }}个时间点</el-tag>
            <el-tag size="small" type="warning">{{ assignedCount }}已分配</el-tag>
          </div>
        </div>
      </div>

      <!-- 线路车辆展示区 -->
      <div v-if="selectedRoute" class="vehicles-section-compact">
        <div class="vehicles-header">
          <h4><el-icon><Van /></el-icon>线路车辆</h4>
          <div class="vehicle-filters">
            <el-radio-group v-model="vehicleFilter" size="small">
              <el-radio-button value="all">全部</el-radio-button>
              <el-radio-button value="available">可用</el-radio-button>
              <el-radio-button value="assigned">已分配</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="vehicles-grid-compact">
          <div
            v-for="vehicle in filteredVehicles"
            :key="vehicle.vehicleId"
            class="vehicle-item-compact"
            :class="{
              'assigned': vehicle.assignedTimes && vehicle.assignedTimes.length > 0,
              'unavailable': vehicle.status !== '1'
            }"
            draggable="true"
            @dragstart="handleVehicleDragStart(vehicle, $event)"
            @dragend="handleVehicleDragEnd"
          >
            <div class="vehicle-icon-small">
              <el-icon><Van /></el-icon>
            </div>
            <div class="vehicle-info-compact">
              <div class="plate-number-small">{{ vehicle.plateNumber }}</div>
              <div v-if="vehicle.assignedTimes && vehicle.assignedTimes.length > 0" class="assignment-info-small">
                {{ vehicle.assignedTimes.length }}趟
              </div>
            </div>
          </div>
        </div>

        <div v-if="filteredVehicles.length === 0" class="empty-vehicles-compact">
          <el-empty description="暂无符合条件的车辆" :image-size="60" />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-if="selectedRoute" class="main-content">
      <!-- 发车时间配置区 -->
      <div class="schedule-section">
        <div class="section-header">
          <h3><el-icon><Clock /></el-icon>发车时间配置</h3>
          <div class="schedule-actions">
            <el-dropdown @command="applyTimeTemplate">
              <el-button size="small" type="success">
                快速配置<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="peak">高峰期模式</el-dropdown-item>
                  <el-dropdown-item command="normal">平峰期模式</el-dropdown-item>
                  <el-dropdown-item command="weekend">周末模式</el-dropdown-item>
                  <el-dropdown-item command="holiday">节假日模式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

          </div>
        </div>

        <!-- 简单SVG时间线 -->
        <div class="timeline-visual">
          <div class="timeline-container">
            <!-- SVG连接线 - 优化的红色渐变拐弯效果 -->
            <svg
              class="timeline-connections"
              v-if="routeTimePoints.length > 1"
              :viewBox="`0 0 ${6 * (140 + 20) + 20} ${Math.ceil(routeTimePoints.length / 6) * (120 + 20) + 20}`"
              preserveAspectRatio="xMidYMid meet"
            >
              <!-- {{ AURA: Modify - 定义绿色渐变和阴影效果 }} -->
              <defs>
                <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#67c23a;stop-opacity:1" />
                  <stop offset="50%" style="stop-color:#85ce61;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#5daf34;stop-opacity:1" />
                </linearGradient>
                <!-- 绿色阴影效果 -->
                <filter id="connectionShadow" x="-50%" y="-50%" width="200%" height="200%">
                  <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(103, 194, 58, 0.3)"/>
                </filter>
              </defs>
              <path
                :d="generateSimpleConnectionPath()"
                stroke="url(#connectionGradient)"
                stroke-width="3"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
                filter="url(#connectionShadow)"
                class="connection-path"
              />
            </svg>

            <!-- 时间节点网格 -->
            <div class="timeline-track">
              <div
                v-for="(timePoint, index) in routeTimePoints"
                :key="`time-${index}`"
                class="timeline-node"
                :class="{
                  'highlight': highlightedTimeIndex === index,
                  'peak-time': timePoint.type === 'peak',
                  'has-vehicle': getAssignedVehicle(timePoint.time)
                }"
                :style="getNodeStyle(index)"
                @drop="handleTimePointDrop($event, timePoint.time)"
                @dragover="handleDragOver"
                @dragenter="handleDragEnter($event, index)"
                @dragleave="handleDragLeave"
              >
                <div class="time-info">
                  <div class="time-text">{{ timePoint.time }}</div>
                  <div class="time-sequence">{{ index + 1 }}</div>
                </div>

                <div class="vehicle-display">
                  <div v-if="getAssignedVehicle(timePoint.time)" class="assigned-vehicle-compact">
                    <div class="vehicle-icon-compact">
                      <el-icon><Van /></el-icon>
                    </div>
                    <div class="vehicle-plate-compact">{{ getAssignedVehicle(timePoint.time)?.plateNumber }}</div>
                    <el-button
                      type="danger"
                      size="small"
                      circle
                      @click="removeAssignment(timePoint.time)"
                      class="remove-btn-compact"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                  <div v-else class="empty-slot">
                    <div class="empty-icon">
                      <el-icon><Plus /></el-icon>
                    </div>
                    <div class="empty-text">拖拽分配</div>
                  </div>
                </div>

                <div class="quick-actions">
                  <el-button
                    v-if="!getAssignedVehicle(timePoint.time)"
                    @click="autoAssignSingle(timePoint.time)"
                    size="small"
                    type="primary"
                    plain
                    class="auto-assign-btn"
                  >
                    自动
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="routeTimePoints.length === 0" class="empty-schedule">
          <el-empty description="暂无发车时间配置" />
        </div>

        <!-- 操作按钮区域 -->
        <div v-if="routeTimePoints.length > 0" class="action-buttons">
          <el-button-group>
            <el-button type="success" @click="autoAssignAll" :loading="autoAssigning" size="large">
              <el-icon><MagicStick /></el-icon>
              一键排班
            </el-button>
            <el-button type="primary" @click="saveSchedule" :loading="saving" size="large">
              <el-icon><Check /></el-icon>
              保存计划
            </el-button>
            <el-button @click="clearAllAssignments" size="large">
              <el-icon><RefreshLeft /></el-icon>
              清空分配
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 未选择线路时的提示 -->
    <div v-else class="no-route-selected">
      <el-result
        icon="info"
        title="请选择公交线路"
        sub-title="选择线路后将显示该线路的车辆和发车时间配置"
      >
        <template #extra>
          <el-button type="primary" @click="focusRouteSelector">
            选择线路
          </el-button>
        </template>
      </el-result>
    </div>



    <!-- 拖拽提示层 -->
    <div v-if="isDragging" class="drag-overlay">
      <div class="drag-hint">
        <el-icon><Rank /></el-icon>
        <span>拖拽到发车时间点进行分配</span>
      </div>
    </div>

    <!-- 排班确认弹窗 -->
    <el-dialog
      v-model="showScheduleConfirm"
      title="一键排班确认"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="schedule-confirm-content">
        <div class="confirm-header">
          <el-icon class="header-icon"><MagicStick /></el-icon>
          <div class="header-text">
            <h3>智能排班方案</h3>
            <p>系统为您生成了以下排班方案，请确认是否应用：</p>
          </div>
        </div>

        <div class="schedule-summary">
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="summary-item">
                <div class="summary-label">待分配时间点</div>
                <div class="summary-value">{{ previewAssignments.length }}个</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <div class="summary-label">涉及车辆</div>
                <div class="summary-value">{{ getInvolvedVehicleCount() }}辆</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <div class="summary-label">分配成功率</div>
                <div class="summary-value">{{ getSuccessRate() }}%</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="schedule-details">
          <div class="details-header">
            <h4>详细分配方案</h4>
          </div>
          <div class="assignment-list">
            <div
              v-for="(assignment, index) in previewAssignments.slice().sort((a, b) => timeToMinutes(a.timePoint) - timeToMinutes(b.timePoint))"
              :key="`${assignment.timePoint}-${assignment.vehicleId}`"
              class="assignment-item"
            >
              <div class="time-info">
                <el-tag :type="getTimePointType(assignment.timePoint)" size="large">
                  {{ assignment.timePoint }}
                </el-tag>
              </div>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
              <div class="vehicle-info">
                <div class="vehicle-selection">
                  <el-select
                    v-model="assignment.vehicleId"
                    @change="handleVehicleChange(assignment, $event)"
                    placeholder="选择车辆"
                    size="small"
                    style="width: 120px;"
                  >
                    <el-option
                      v-for="vehicle in getAvailableVehiclesForTime(assignment.timePoint, assignment.vehicleId)"
                      :key="vehicle.vehicleId"
                      :label="vehicle.plateNumber"
                      :value="vehicle.vehicleId"
                    >
                      <span style="float: left">{{ vehicle.plateNumber }}</span>
                      <span style="float: right; color: #8492a6; font-size: 12px">
                        {{ getVehicleStatusText(vehicle.vehicleId, assignment.timePoint) }}
                      </span>
                    </el-option>
                  </el-select>
                </div>
                <div class="vehicle-trips">第{{ getVehicleTripCount(assignment.vehicleId, assignment.timePoint) }}趟</div>
              </div>
              <div class="assignment-actions">
                <el-button
                  type="danger"
                  size="small"
                  plain
                  @click="removePreviewAssignment(assignment)"
                  :icon="Close"
                >
                </el-button>
              </div>
            </div>
          </div>

          <div v-if="failedAssignments.length > 0" class="failed-assignments">
            <div class="failed-header">
              <h4>无法分配的时间点</h4>
              <el-button
                type="primary"
                size="small"
                plain
                @click="addManualAssignment"
              >
                手动分配
              </el-button>
            </div>
            <div class="failed-list">
              <el-tag
                v-for="timePoint in failedAssignments"
                :key="timePoint"
                type="danger"
                size="small"
              >
                {{ timePoint }}
              </el-tag>
            </div>
            <p class="failed-reason">原因：无可用车辆或时间冲突</p>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelScheduleConfirm">取消</el-button>
          <el-button
            type="primary"
            @click="confirmScheduleAssign"
            :loading="applying"
          >
            确认应用
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Share, MagicStick, Check, RefreshLeft, Position, Van, Clock,
  ArrowDown, Setting, Plus, Close, MoreFilled, Rank, ArrowRight
} from '@element-plus/icons-vue'

// 接口定义
interface BusRoute {
  routeId: number | string
  routeName: string
  routeCode: string
  distance: number
  stationCount: number
  description: string
  vehicleIds: (number | string)[]
  timePoints: TimePoint[]
}

interface Vehicle {
  vehicleId: number | string
  plateNumber: string
  vehicleNumber: string
  vehicleType: 'bus' | 'electric_bus' | 'hybrid_bus' | 'minibus'
  seatCount: number
  status: '1' | '0' | '2' | '3'
  routeId?: number | string
  assignedTimes?: string[] // 改为数组，记录所有分配的时间点
}

interface TimePoint {
  time: string
  type?: 'peak' | 'normal' | 'weekend' | 'holiday'
  frequency?: number
}

interface Assignment {
  timePoint: string
  vehicleId: number | string
  plateNumber: string
}

// 响应式数据
const routes = ref<BusRoute[]>([])
const vehicles = ref<Vehicle[]>([])
const selectedRouteId = ref<number | string | null>(null)
const assignments = ref<Assignment[]>([])
const routesLoading = ref(false)
const autoAssigning = ref(false)
const saving = ref(false)
const isDragging = ref(false)
const highlightedTimeIndex = ref(-1)

// {{ AURA: Add - 排班确认弹窗相关状态 }}
const showScheduleConfirm = ref(false)
const previewAssignments = ref<Assignment[]>([])
const failedAssignments = ref<string[]>([])
const applying = ref(false)
const vehicleFilter = ref('all')

// 简单的蛇形排列计算
const getSnakePosition = (index: number) => {
  const cols = 6
  const row = Math.floor(index / cols)
  const col = index % cols
  const isReverseRow = row % 2 === 1

  return {
    row,
    col: isReverseRow ? cols - 1 - col : col,
    isReverse: isReverseRow
  }
}

// 节点CSS Grid定位
const getNodeStyle = (index: number) => {
  const pos = getSnakePosition(index)
  return {
    gridRow: pos.row + 1,
    gridColumn: pos.col + 1
  }
}

// 生成连接路径 - 在每一行拐弯时延伸出来再拐弯
const generateSimpleConnectionPath = () => {
  if (routeTimePoints.value.length < 2) return ''

  const cols = 6
  // 需要与CSS完全匹配的数值
  const nodeWidth = 140 // 节点宽度
  const nodeHeight = 120 // 节点高度
  const gap = 20 // CSS gap值
  const containerPadding = 20 // timeline-track的padding
  const extensionLength = 100 // {{ AURA: Modify - 延伸距离增加到约半个卡片加间距的距离 }}

  let path = ''

  for (let i = 0; i < routeTimePoints.value.length; i++) {
    const pos = getSnakePosition(i)
    const nextPos = i < routeTimePoints.value.length - 1 ? getSnakePosition(i + 1) : null

    // 计算节点的实际中心位置
    const x = containerPadding + pos.col * (nodeWidth + gap) + nodeWidth / 2
    const y = containerPadding + pos.row * (nodeHeight + gap) + nodeHeight / 2

    if (i === 0) {
      path += `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }

    // {{ AURA: Add - 在需要换行时添加延伸效果 }}
    if (nextPos && nextPos.row !== pos.row) {
      // 下一个节点在不同行，需要拐弯
      if (pos.col === cols - 1) {
        // 当前在行末，向右延伸再向下
        const extensionX = x + extensionLength
        const nextY = containerPadding + nextPos.row * (nodeHeight + gap) + nodeHeight / 2
        path += ` L ${extensionX} ${y}` // 向右延伸
        path += ` L ${extensionX} ${nextY}` // 向下拐弯
      } else if (pos.col === 0) {
        // 当前在行首，向左延伸再向下
        const extensionX = x - extensionLength
        const nextY = containerPadding + nextPos.row * (nodeHeight + gap) + nodeHeight / 2
        path += ` L ${extensionX} ${y}` // 向左延伸
        path += ` L ${extensionX} ${nextY}` // 向下拐弯
      }
    }
  }

  return path
}

// 计算属性
const selectedRoute = computed(() => {
  return routes.value.find(r => r.routeId === selectedRouteId.value) || null
})

const routeVehicles = computed(() => {
  if (!selectedRoute.value) return []
  return vehicles.value.filter(v =>
    selectedRoute.value?.vehicleIds.includes(v.vehicleId)
  )
})

const routeTimePoints = computed(() => {
  return selectedRoute.value?.timePoints || []
})

const filteredVehicles = computed(() => {
  let filtered = routeVehicles.value

  switch (vehicleFilter.value) {
    case 'available':
      filtered = filtered.filter(v => v.status === '1')
      break
    case 'assigned':
      filtered = filtered.filter(v => v.assignedTimes && v.assignedTimes.length > 0)
      break
  }

  return filtered
})

const assignedCount = computed(() => {
  return assignments.value.length
})

// 辅助函数：时间转换为分钟数
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

// 事件处理
const handleRouteChange = (routeId: number | string) => {
  selectedRouteId.value = routeId
  // 清空之前的分配
  clearAllAssignments()
  ElMessage.success(`已选择线路：${selectedRoute.value?.routeName}`)
}

const handleVehicleDragStart = (vehicle: Vehicle, event: DragEvent) => {
  if (vehicle.status !== '1') {
    event.preventDefault()
    ElMessage.warning('该车辆状态不可用')
    return
  }

  event.dataTransfer!.setData('text/plain', JSON.stringify({
    type: 'vehicle',
    vehicleId: vehicle.vehicleId,
    plateNumber: vehicle.plateNumber
  }))

  isDragging.value = true
}

const handleVehicleDragEnd = () => {
  isDragging.value = false
  highlightedTimeIndex.value = -1
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragEnter = (event: DragEvent, index: number) => {
  event.preventDefault()
  highlightedTimeIndex.value = index
}

const handleDragLeave = (event: DragEvent) => {
  // 检查是否真的离开了拖拽区域
  if (!event.currentTarget || !(event.currentTarget as Element).contains(event.relatedTarget as Node)) {
    highlightedTimeIndex.value = -1
  }
}

const handleTimePointDrop = (event: DragEvent, timePoint: string) => {
  event.preventDefault()
  highlightedTimeIndex.value = -1
  isDragging.value = false

  try {
    const dragData = JSON.parse(event.dataTransfer!.getData('text/plain'))

    if (dragData.type === 'vehicle') {
      assignVehicleToTimePoint(dragData.vehicleId, dragData.plateNumber, timePoint)
    }
  } catch (error) {
    ElMessage.error('分配失败')
  }
}

// 辅助函数：检查车辆在指定时间是否可用（考虑行程时间）
const isVehicleAvailableAtTime = (vehicleId: number | string, targetTime: string): boolean => {
  const vehicle = vehicles.value.find(v => v.vehicleId === vehicleId)
  if (!vehicle || !vehicle.assignedTimes || vehicle.assignedTimes.length === 0) {
    return true
  }

  const targetMinutes = timeToMinutes(targetTime)
  const tripDuration = 90 // 假设一趟车需要90分钟（包括行程和休息时间）

  // 检查是否与已分配的时间冲突
  for (const assignedTime of vehicle.assignedTimes) {
    const assignedMinutes = timeToMinutes(assignedTime)
    const timeDiff = Math.abs(targetMinutes - assignedMinutes)

    if (timeDiff < tripDuration) {
      return false // 时间冲突
    }
  }

  return true
}

// 辅助函数：获取车辆的分配次数
const getVehicleAssignmentCount = (vehicleId: number | string): number => {
  const vehicle = vehicles.value.find(v => v.vehicleId === vehicleId)
  return vehicle?.assignedTimes?.length || 0
}

// 工具函数

const getVehicleTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    bus: '普通公交',
    electric_bus: '电动公交',
    hybrid_bus: '混动公交',
    minibus: '小型公交'
  }
  return typeMap[type] || type
}

const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'success',
    '0': 'info',
    '2': 'warning',
    '3': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '正常',
    '0': '停用',
    '2': '维修中',
    '3': '故障'
  }
  return statusMap[status] || '未知'
}

const getAssignedVehicle = (timePoint: string): Vehicle | null => {
  const assignment = assignments.value.find(a => a.timePoint === timePoint)
  if (!assignment) return null
  return vehicles.value.find(v => v.vehicleId === assignment.vehicleId) || null
}

const assignVehicleToTimePoint = (vehicleId: number | string, plateNumber: string, timePoint: string) => {
  // 检查该时间点是否已有分配
  const existingAssignment = assignments.value.find(a => a.timePoint === timePoint)
  if (existingAssignment) {
    ElMessage.warning(`时间点 ${timePoint} 已有车辆分配`)
    return
  }

  // 检查车辆在该时间是否可用（时间冲突检测）
  if (!isVehicleAvailableAtTime(vehicleId, timePoint)) {
    ElMessage.warning(`车辆 ${plateNumber} 在 ${timePoint} 时间冲突，请选择其他时间`)
    return
  }

  // 创建新分配
  assignments.value.push({
    timePoint,
    vehicleId,
    plateNumber
  })

  // 更新车辆的分配时间数组
  const vehicle = vehicles.value.find(v => v.vehicleId === vehicleId)
  if (vehicle) {
    if (!vehicle.assignedTimes) {
      vehicle.assignedTimes = []
    }
    vehicle.assignedTimes.push(timePoint)
    // 按时间排序
    vehicle.assignedTimes.sort()
  }

  const assignmentCount = getVehicleAssignmentCount(vehicleId)
  ElMessage.success(`车辆 ${plateNumber} 已分配到 ${timePoint}（第${assignmentCount}趟）`)
}

const removeAssignment = (timePoint: string) => {
  const assignmentIndex = assignments.value.findIndex(a => a.timePoint === timePoint)
  if (assignmentIndex === -1) return

  const assignment = assignments.value[assignmentIndex]

  // 移除分配
  assignments.value.splice(assignmentIndex, 1)

  // 从车辆的分配时间数组中移除该时间点
  const vehicle = vehicles.value.find(v => v.vehicleId === assignment.vehicleId)
  if (vehicle && vehicle.assignedTimes) {
    const timeIndex = vehicle.assignedTimes.indexOf(timePoint)
    if (timeIndex > -1) {
      vehicle.assignedTimes.splice(timeIndex, 1)
    }
  }

  ElMessage.success(`已取消 ${timePoint} 的车辆分配`)
}

const handleTimePointAction = (command: string, timePoint: string) => {
  switch (command) {
    case 'auto-assign':
      autoAssignSingle(timePoint)
      break
    case 'clear':
      removeAssignment(timePoint)
      break
    case 'edit':
      // 编辑单个时间点
      ElMessage.info('编辑功能开发中')
      break
  }
}

const autoAssignSingle = (timePoint: string) => {
  // 获取在该时间点可用的车辆（按分配次数排序，优先选择分配次数少的）
  const availableVehicles = routeVehicles.value
    .filter(v => v.status === '1' && isVehicleAvailableAtTime(v.vehicleId, timePoint))
    .sort((a, b) => getVehicleAssignmentCount(a.vehicleId) - getVehicleAssignmentCount(b.vehicleId))

  if (availableVehicles.length === 0) {
    ElMessage.warning('该时间点没有可用的车辆')
    return
  }

  // 选择分配次数最少的车辆
  const vehicle = availableVehicles[0]
  assignVehicleToTimePoint(vehicle.vehicleId, vehicle.plateNumber, timePoint)
}

const autoAssignAll = async () => {
  if (!selectedRoute.value) return

  // {{ AURA: Modify - 获取未分配的时间点，并按时间排序 }}
  const unassignedTimePoints = routeTimePoints.value
    .filter(tp => !assignments.value.some(a => a.timePoint === tp.time))
    .sort((a, b) => timeToMinutes(a.time) - timeToMinutes(b.time))

  if (unassignedTimePoints.length === 0) {
    ElMessage.warning('所有时间点都已分配')
    return
  }

  autoAssigning.value = true

  try {
    // {{ AURA: Modify - 生成排班预览方案，不直接分配 }}
    await new Promise(resolve => setTimeout(resolve, 1000))

    const tempAssignments: Assignment[] = []
    const tempFailedAssignments: string[] = []

    // {{ AURA: Modify - 为每个未分配的时间点寻找最佳车辆，考虑预览过程中的临时分配 }}
    for (const tp of unassignedTimePoints) {
      const availableVehicles = routeVehicles.value
        .filter(v => {
          // 1. 车辆状态必须可用
          if (v.status !== '1') return false

          // 2. 检查与现有分配的时间冲突
          if (!isVehicleAvailableAtTime(v.vehicleId, tp.time)) return false

          // 3. 检查与预览分配的时间冲突
          const hasConflictInPreview = tempAssignments.some(tempAssign => {
            if (tempAssign.vehicleId !== v.vehicleId) return false
            const timeDiff = Math.abs(timeToMinutes(tempAssign.timePoint) - timeToMinutes(tp.time))
            return timeDiff < 60 // 60分钟间隔限制
          })

          return !hasConflictInPreview
        })
        .sort((a, b) => {
          // 按现有分配次数 + 预览分配次数排序
          const aExistingCount = getVehicleAssignmentCount(a.vehicleId)
          const aPreviewCount = tempAssignments.filter(ta => ta.vehicleId === a.vehicleId).length
          const bExistingCount = getVehicleAssignmentCount(b.vehicleId)
          const bPreviewCount = tempAssignments.filter(ta => ta.vehicleId === b.vehicleId).length

          return (aExistingCount + aPreviewCount) - (bExistingCount + bPreviewCount)
        })

      if (availableVehicles.length > 0) {
        const vehicle = availableVehicles[0]
        tempAssignments.push({
          timePoint: tp.time,
          vehicleId: vehicle.vehicleId,
          plateNumber: vehicle.plateNumber
        })
      } else {
        tempFailedAssignments.push(tp.time)
      }
    }

    // {{ AURA: Add - 设置预览数据并显示确认弹窗 }}
    previewAssignments.value = tempAssignments
    failedAssignments.value = tempFailedAssignments
    showScheduleConfirm.value = true

  } finally {
    autoAssigning.value = false
  }
}

const clearAllAssignments = () => {
  assignments.value = []
  vehicles.value.forEach(v => {
    v.assignedTimes = []
  })
  ElMessage.success('已清空所有分配')
}

// {{ AURA: Add - 排班确认弹窗相关方法 }}
const getInvolvedVehicleCount = () => {
  const vehicleIds = new Set(previewAssignments.value.map(a => a.vehicleId))
  return vehicleIds.size
}

const getSuccessRate = () => {
  const totalCount = previewAssignments.value.length + failedAssignments.value.length
  if (totalCount === 0) return 0
  return Math.round((previewAssignments.value.length / totalCount) * 100)
}

const getTimePointType = (timePoint: string) => {
  const timeMinutes = timeToMinutes(timePoint)
  // 早高峰: 7:00-9:00, 晚高峰: 17:00-19:00
  if ((timeMinutes >= 420 && timeMinutes <= 540) || (timeMinutes >= 1020 && timeMinutes <= 1140)) {
    return 'warning' // 高峰期用橙色
  }
  return 'primary' // 普通时段用蓝色
}

const getVehicleTripCount = (vehicleId: number | string, currentTimePoint?: string) => {
  // {{ AURA: Modify - 计算该车辆的总趟次，如果提供当前时间点，则计算到该时间点为止的趟次 }}
  const existingCount = getVehicleAssignmentCount(vehicleId)

  if (currentTimePoint) {
    // 计算在当前时间点之前（包括当前）的预览分配数量
    const previewCountBeforeCurrent = previewAssignments.value
      .filter(a => a.vehicleId === vehicleId)
      .filter(a => timeToMinutes(a.timePoint) <= timeToMinutes(currentTimePoint))
      .length
    return existingCount + previewCountBeforeCurrent
  } else {
    // 计算总的预览分配数量
    const previewCount = previewAssignments.value.filter(a => a.vehicleId === vehicleId).length
    return existingCount + previewCount
  }
}

const cancelScheduleConfirm = () => {
  showScheduleConfirm.value = false
  previewAssignments.value = []
  failedAssignments.value = []
}

// {{ AURA: Add - 弹窗编辑功能相关方法 }}
const getAvailableVehiclesForTime = (timePoint: string, currentVehicleId?: number | string) => {
  return routeVehicles.value.filter(v => {
    // 1. 车辆状态必须可用
    if (v.status !== '1') return false

    // 2. 如果是当前选中的车辆，允许保持选择
    if (currentVehicleId && v.vehicleId === currentVehicleId) return true

    // 3. 检查与现有分配的时间冲突
    if (!isVehicleAvailableAtTime(v.vehicleId, timePoint)) return false

    // 4. 检查与其他预览分配的时间冲突
    const hasConflictInOtherPreview = previewAssignments.value.some(previewAssign => {
      if (previewAssign.vehicleId !== v.vehicleId) return false
      if (previewAssign.timePoint === timePoint) return false // 排除当前时间点
      const timeDiff = Math.abs(timeToMinutes(previewAssign.timePoint) - timeToMinutes(timePoint))
      return timeDiff < 60 // 60分钟间隔限制
    })

    return !hasConflictInOtherPreview
  })
}

const getVehicleStatusText = (vehicleId: number | string, timePoint: string) => {
  const existingCount = getVehicleAssignmentCount(vehicleId)
  const previewCount = previewAssignments.value.filter(a =>
    a.vehicleId === vehicleId && timeToMinutes(a.timePoint) <= timeToMinutes(timePoint)
  ).length
  const totalTrips = existingCount + previewCount
  return `第${totalTrips}趟`
}

const handleVehicleChange = (assignment: Assignment, newVehicleId: number | string) => {
  // 更新车牌号
  const newVehicle = vehicles.value.find(v => v.vehicleId === newVehicleId)
  if (newVehicle) {
    assignment.plateNumber = newVehicle.plateNumber
  }

  // 触发响应式更新
  const index = previewAssignments.value.findIndex(a =>
    a.timePoint === assignment.timePoint && a.vehicleId === assignment.vehicleId
  )
  if (index > -1) {
    previewAssignments.value[index] = { ...assignment }
  }
}

const removePreviewAssignment = (assignment: Assignment) => {
  const index = previewAssignments.value.findIndex(a =>
    a.timePoint === assignment.timePoint && a.vehicleId === assignment.vehicleId
  )
  if (index > -1) {
    previewAssignments.value.splice(index, 1)
    // 将该时间点添加到失败列表
    if (!failedAssignments.value.includes(assignment.timePoint)) {
      failedAssignments.value.push(assignment.timePoint)
    }
  }
}

const addManualAssignment = () => {
  // 获取所有失败的时间点，让用户可以手动分配
  if (failedAssignments.value.length === 0) {
    ElMessage.info('所有时间点都已分配')
    return
  }

  // 这里可以添加手动分配的逻辑
  ElMessage.info('手动分配功能开发中')
}

const confirmScheduleAssign = async () => {
  applying.value = true

  try {
    // 应用所有预览的分配
    let successCount = 0
    for (const assignment of previewAssignments.value) {
      // 再次检查车辆是否可用（防止在确认期间发生变化）
      if (isVehicleAvailableAtTime(assignment.vehicleId, assignment.timePoint)) {
        // 静默分配，不显示单个成功消息
        assignments.value.push(assignment)

        // 更新车辆的分配时间数组
        const vehicle = vehicles.value.find(v => v.vehicleId === assignment.vehicleId)
        if (vehicle) {
          if (!vehicle.assignedTimes) {
            vehicle.assignedTimes = []
          }
          vehicle.assignedTimes.push(assignment.timePoint)
          vehicle.assignedTimes.sort()
        }
        successCount++
      }
    }

    // 关闭弹窗
    showScheduleConfirm.value = false

    // 显示总体结果
    if (successCount > 0) {
      ElMessage.success(`一键排班完成！成功分配 ${successCount} 个班次`)
      if (failedAssignments.value.length > 0) {
        ElMessage.warning(`${failedAssignments.value.length} 个时间点无法分配`)
      }
    } else {
      ElMessage.warning('排班分配失败，请检查车辆可用性')
    }

  } finally {
    applying.value = false
    previewAssignments.value = []
    failedAssignments.value = []
  }
}

const saveSchedule = async () => {
  if (assignments.value.length === 0) {
    ElMessage.warning('请先进行排班分配')
    return
  }

  saving.value = true
  try {
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    ElMessage.success('排班计划保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const applyTimeTemplate = (template: string) => {
  let templateTimes: TimePoint[] = []

  switch (template) {
    case 'peak':
      templateTimes = [
        { time: '06:00', type: 'peak' },
        { time: '06:15', type: 'peak' },
        { time: '06:30', type: 'peak' },
        { time: '06:45', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:15', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '07:45', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '17:00', type: 'peak' },
        { time: '17:15', type: 'peak' },
        { time: '17:30', type: 'peak' },
        { time: '17:45', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:15', type: 'peak' },
        { time: '18:30', type: 'peak' }
      ]
      break
    case 'normal':
      templateTimes = [
        { time: '06:00', type: 'normal' },
        { time: '06:30', type: 'normal' },
        { time: '07:00', type: 'normal' },
        { time: '08:00', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '11:00', type: 'normal' },
        { time: '12:00', type: 'normal' },
        { time: '14:00', type: 'normal' },
        { time: '15:00', type: 'normal' },
        { time: '16:00', type: 'normal' },
        { time: '17:00', type: 'normal' },
        { time: '18:00', type: 'normal' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:00', type: 'normal' }
      ]
      break
    case 'weekend':
      templateTimes = [
        { time: '07:00', type: 'weekend' },
        { time: '08:00', type: 'weekend' },
        { time: '09:00', type: 'weekend' },
        { time: '10:00', type: 'weekend' },
        { time: '11:00', type: 'weekend' },
        { time: '14:00', type: 'weekend' },
        { time: '15:00', type: 'weekend' },
        { time: '16:00', type: 'weekend' },
        { time: '17:00', type: 'weekend' },
        { time: '18:00', type: 'weekend' },
        { time: '19:00', type: 'weekend' },
        { time: '20:00', type: 'weekend' },
        { time: '21:00', type: 'weekend' }
      ]
      break
    case 'holiday':
      templateTimes = [
        { time: '08:00', type: 'holiday' },
        { time: '09:00', type: 'holiday' },
        { time: '10:00', type: 'holiday' },
        { time: '11:00', type: 'holiday' },
        { time: '14:00', type: 'holiday' },
        { time: '15:00', type: 'holiday' },
        { time: '16:00', type: 'holiday' },
        { time: '17:00', type: 'holiday' },
        { time: '18:00', type: 'holiday' },
        { time: '19:00', type: 'holiday' },
        { time: '20:00', type: 'holiday' }
      ]
      break
  }

  if (selectedRoute.value) {
    selectedRoute.value.timePoints = templateTimes
    clearAllAssignments()
    ElMessage.success(`已应用${template}模式的发车时间配置`)
  }
}



const focusRouteSelector = () => {
  // 聚焦到线路选择器
}

// 初始化数据
const initializeData = () => {
  // 模拟线路数据
  routes.value = [
    {
      routeId: 1,
      routeName: '1路公交',
      routeCode: 'BUS-001',
      distance: 15.2,
      stationCount: 28,
      description: '火车站 - 市政府 - 商业中心',
      vehicleIds: [1, 2, 3, 4],
      timePoints: [
        { time: '06:00', type: 'peak' },
        { time: '06:30', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '08:00', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '17:00', type: 'peak' },
        { time: '17:30', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:30', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:00', type: 'normal' }
      ]
    },
    {
      routeId: 2,
      routeName: '2路公交',
      routeCode: 'BUS-002',
      distance: 12.8,
      stationCount: 22,
      description: '体育场 - 大学城 - 科技园',
      vehicleIds: [5, 6, 7],
      timePoints: [
        { time: '06:30', type: 'normal' },
        { time: '07:00', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '11:00', type: 'normal' },
        { time: '17:00', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:30', type: 'normal' }
      ]
    },
    {
      routeId: 3,
      routeName: '3路公交',
      routeCode: 'BUS-003',
      distance: 18.5,
      stationCount: 35,
      description: '北站 - 市中心 - 南站',
      vehicleIds: [8, 9, 10, 11, 12],
      timePoints: [
        { time: '05:30', type: 'normal' },
        { time: '06:00', type: 'peak' },
        { time: '06:20', type: 'peak' },
        { time: '06:40', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:20', type: 'peak' },
        { time: '07:40', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '08:30', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '09:30', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '16:30', type: 'peak' },
        { time: '17:00', type: 'peak' },
        { time: '17:20', type: 'peak' },
        { time: '17:40', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:20', type: 'peak' },
        { time: '18:40', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:30', type: 'normal' }
      ]
    }
  ]

  // 模拟车辆数据
  vehicles.value = [
    { vehicleId: 1, plateNumber: '京A12345', vehicleNumber: 'V001', vehicleType: 'bus', seatCount: 35, status: '1', routeId: 1 },
    { vehicleId: 2, plateNumber: '京A12346', vehicleNumber: 'V002', vehicleType: 'electric_bus', seatCount: 30, status: '1', routeId: 1 },
    { vehicleId: 3, plateNumber: '京A12347', vehicleNumber: 'V003', vehicleType: 'bus', seatCount: 40, status: '1', routeId: 1 },
    { vehicleId: 4, plateNumber: '京A12348', vehicleNumber: 'V004', vehicleType: 'hybrid_bus', seatCount: 32, status: '2', routeId: 1 },
    { vehicleId: 5, plateNumber: '京A12349', vehicleNumber: 'V005', vehicleType: 'electric_bus', seatCount: 28, status: '1', routeId: 2 },
    { vehicleId: 6, plateNumber: '京A12350', vehicleNumber: 'V006', vehicleType: 'bus', seatCount: 35, status: '1', routeId: 2 },
    { vehicleId: 7, plateNumber: '京A12351', vehicleNumber: 'V007', vehicleType: 'electric_bus', seatCount: 30, status: '1', routeId: 2 },
    { vehicleId: 8, plateNumber: '京A12352', vehicleNumber: 'V008', vehicleType: 'bus', seatCount: 45, status: '1', routeId: 3 },
    { vehicleId: 9, plateNumber: '京A12353', vehicleNumber: 'V009', vehicleType: 'bus', seatCount: 45, status: '1', routeId: 3 },
    { vehicleId: 10, plateNumber: '京A12354', vehicleNumber: 'V010', vehicleType: 'electric_bus', seatCount: 35, status: '1', routeId: 3 },
    { vehicleId: 11, plateNumber: '京A12355', vehicleNumber: 'V011', vehicleType: 'hybrid_bus', seatCount: 38, status: '1', routeId: 3 },
    { vehicleId: 12, plateNumber: '京A12356', vehicleNumber: 'V012', vehicleType: 'bus', seatCount: 45, status: '3', routeId: 3 }
  ]
}

// 生命周期
onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.route-schedule-container {
  min-height: 100vh;
  background: #f8fafb;
  padding: 0;
}

/* 简单SVG时间线样式 */
.timeline-visual {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-container {
  width: 100%;
  position: relative;
  min-height: 400px;
}

/* SVG连接线样式 - 优化层级关系 */
.timeline-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5; /* {{ AURA: Modify - 大幅提升连接线层级，确保在所有节点元素之上 }} */
}

/* {{ AURA: Modify - 简化的连接路径效果 }} */
.connection-path {
  transition: all 0.3s ease;
  opacity: 0.8;
}

.connection-path:hover {
  opacity: 1;
}

/* {{ AURA: Modify - 节点悬停时绿色连接线高亮效果 }} */
.timeline-node:hover ~ .timeline-connections .connection-path,
.timeline-container:hover .connection-path {
  stroke-width: 4;
  filter: url(#connectionShadow) drop-shadow(0 0 6px rgba(103, 194, 58, 0.6));
}

.timeline-track {
  display: grid;
  grid-template-columns: repeat(6, 140px);
  grid-auto-rows: 120px;
  gap: 20px;
  position: relative;
  z-index: 1; /* {{ AURA: Modify - 确保节点在连接线下方，但保持交互性 }} */
  justify-content: center;
  padding: 20px;
}

.timeline-node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 140px;
  height: 120px;
  padding: 8px;
  border-radius: 6px;
  background: #ffffff; /* {{ AURA: Modify - 使用纯白色背景，移除透明度 }} */
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* {{ AURA: Modify - 确保节点层级低于连接线 }} */
  z-index: 1;
}

.timeline-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.timeline-node.highlight {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
}

.timeline-node.peak-time {
  border-color: #3498db;
  background: linear-gradient(135deg, #e8f4fd 0%, #d6eafc 100%); /* {{ AURA: Modify - 使用简约的蓝色渐变，大气专业 }} */
  color: #2c3e50; /* {{ AURA: Modify - 深蓝色文字，简约专业 }} */
  position: relative;
  z-index: 1; /* 确保层级低于连接线 */
  box-shadow: 0 2px 12px rgba(52, 152, 219, 0.15); /* {{ AURA: Add - 添加蓝色阴影提升质感 }} */
}

/* {{ AURA: Add - 高峰期节点内的按钮样式优化 }} */
.timeline-node.peak-time .auto-assign-btn {
  background: rgba(52, 152, 219, 0.1) !important;
  border-color: rgba(52, 152, 219, 0.3) !important;
  color: #3498db !important;
}

.timeline-node.peak-time .auto-assign-btn:hover {
  background: rgba(52, 152, 219, 0.2) !important;
  border-color: rgba(52, 152, 219, 0.5) !important;
  color: #2980b9 !important;
}

.timeline-node.has-vehicle {
  border-color: #67c23a;
  background: #ffffff !important; /* {{ AURA: Modify - 使用纯白色背景，移除透明效果 }} */
}

.time-info {
  text-align: center;
  padding: 4px 0;
  background: #f8f9fa;
  border-radius: 4px;
  position: relative;
}

.time-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.time-sequence {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #3b82f6;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 6; /* {{ AURA: Modify - 时间序号层级最高，确保始终在最顶层 }} */
}

.vehicle-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  margin: 6px 0;
}

.assigned-vehicle-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #10b981 !important;
  color: white;
  border-radius: 16px;
  position: relative;
  width: 100%;
  justify-content: center;
  /* {{ AURA: Remove - 移除半透明效果，防止线条透过 }} */
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  border: 2px solid #ffffff !important; /* {{ AURA: Modify - 使用完全不透明的白色边框 }} */
  z-index: 3; /* 确保车辆信息在连接线之上 */
}

.vehicle-icon-compact {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vehicle-plate-compact {
  font-size: 11px;
  font-weight: 600;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn-compact {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  min-height: 12px;
  padding: 0;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #c0c4cc;
  padding: 4px;
}

.empty-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 10px;
  text-align: center;
}

.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.auto-assign-btn {
  font-size: 10px;
  padding: 2px 8px;
  height: 20px;
}



.action-buttons {
  margin-top: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* {{ AURA: Modify - 合并后的线路和车辆卡片样式 }} */
.route-and-vehicles-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e8eaed;
}

/* {{ AURA: Add - 线路选择器区域样式 }} */
.route-selector-section {
  margin-bottom: 20px;
}

.selector-header {
  display: flex;
  align-items: center; /* {{ AURA: Modify - 改为居中对齐，与单行信息匹配 }} */
  justify-content: space-between;
  gap: 24px;
  margin-bottom: 16px;
}

.selector-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  min-width: 140px;
}

/* {{ AURA: Modify - 单行内联线路信息样式 }} */
.route-info-inline {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
  background: #f8fafc;
  border-radius: 6px;
  padding: 0 16px;
  border-left: 4px solid #667eea;
  height: 40px; /* 与el-select默认高度保持一致 */
  overflow: hidden;
}

/* {{ AURA: Modify - 内联元素样式 }} */
.route-info-inline .route-name {
  font-size: 15px;
  font-weight: 600;
  color: #1a202c;
  white-space: nowrap;
  flex-shrink: 0;
}

.route-info-inline .route-code {
  background: #667eea;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.route-info-inline .route-stats {
  color: #64748b;
  font-size: 13px;
  white-space: nowrap;
  flex-shrink: 0;
}

.route-info-inline .el-tag {
  flex-shrink: 0;
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

/* {{ AURA: Delete - 移除不再使用的旧样式 }} */

.main-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 0 16px 16px 16px;
}

/* {{ AURA: Add - 紧凑型车辆展示区域 }} */
.vehicles-section-compact {
  margin-top: 0;
}

.vehicles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.vehicles-header h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.vehicles-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 10px;
  margin-bottom: 12px;
}

.vehicle-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  border: 1.5px solid #e5e7eb;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
  background: #fafafa;
  min-height: 44px;
}

.vehicle-item-compact:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
  transform: translateY(-1px);
  background: white;
}

.vehicle-item-compact.assigned {
  border-color: #10b981;
  background: #f0fdf4;
}

.vehicle-item-compact.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
}

.vehicle-icon-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #6366f1;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
}

.vehicle-info-compact {
  flex: 1;
  min-width: 0;
}

.plate-number-small {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 2px;
}

.assignment-info-small {
  font-size: 10px;
  color: #10b981;
  font-weight: 500;
}

.empty-vehicles-compact {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.schedule-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e8eaed;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #1f2937;
}

.vehicle-filters {
  display: flex;
  gap: 12px;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.vehicle-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 2px solid #e4e7ed;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.vehicle-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.vehicle-item.assigned {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff, #ecf5ff);
}

.vehicle-item.unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.vehicle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #10b981;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.vehicle-info {
  flex: 1;
}

.plate-number {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.vehicle-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.seat-info {
  font-size: 12px;
  color: #606266;
}

.assignment-info {
  font-size: 10px;
  color: #67c23a;
  font-weight: 600;
  margin-top: 1px;
}

.assigned-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #67c23a;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeline-grid {
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.timeline-header {
  display: grid;
  grid-template-columns: 150px 1fr 80px;
  background: #f8fafb;
  font-weight: 500;
  color: #1f2937;
}

.timeline-header > div {
  padding: 18px 16px;
  border-right: 1px solid #e8eaed;
}

.timeline-header > div:last-child {
  border-right: none;
}

.timeline-row {
  display: grid;
  grid-template-columns: 150px 1fr 80px;
  border-top: 1px solid #f0f2f5;
  transition: background-color 0.2s ease;
}

.timeline-row.highlight {
  background: rgba(59, 130, 246, 0.05);
}

.timeline-row > div {
  padding: 18px 16px;
  border-right: 1px solid #f0f2f5;
  display: flex;
  align-items: center;
}

.timeline-row > div:last-child {
  border-right: none;
  justify-content: center;
}

.time-cell {
  flex-direction: column;
  align-items: flex-start;
}

.time-display {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.time-type {
  font-size: 12px;
  color: #909399;
}

.assignment-cell {
  min-height: 60px;
}

.assigned-vehicle .vehicle-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #10b981;
  color: white;
  border-radius: 20px;
  position: relative;
}

.vehicle-icon-small {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vehicle-details .plate {
  font-weight: 600;
  font-size: 12px;
}

.vehicle-details .type {
  font-size: 10px;
  opacity: 0.8;
}

.remove-btn {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  min-height: 16px;
}

.drop-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  color: #c0c4cc;
  font-size: 12px;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 60px;
}

.timeline-row.highlight .drop-zone {
  border-color: #409eff;
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.empty-vehicles,
.empty-schedule {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.no-route-selected {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.time-config-content {
  padding: 16px;
}

.config-header {
  margin-bottom: 24px;
}

.time-preview {
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.time-tag {
  margin: 2px 4px 2px 0;
}

.preview-empty {
  text-align: center;
  color: #c0c4cc;
  font-size: 14px;
  padding: 20px;
}

.drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: none;
}

.drag-hint {
  background: #409eff;
  color: white;
  padding: 16px 24px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
/* {{ AURA: Modify - 响应式布局优化 }} */
@media (max-width: 1200px) {
  .vehicles-grid-compact {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 8px;
  }
}

/* 图形化时间线样式 */
.timeline-visual {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移除布局控制器样式 */

.timeline-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  padding: 0; /* 移除额外的padding，让SVG和Grid对齐 */
  position: relative;
  min-height: 200px;
}

/* SVG连接线样式 */
.timeline-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0; /* 确保连接线在最底层 */
}

/* 移除时间轴背景线 */

/* 移除蛇形布局样式 */

/* 移除蛇形节点样式 */

/* 时间车辆显示 */
.time-vehicle-display {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.time-info {
  text-align: center;
  padding: 4px 0;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 移除蛇形时间信息样式 */

.time-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

/* 移除蛇形时间文本样式 */

/* 移除原时间序号样式，使用新的绝对定位样式 */

.vehicle-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.assigned-vehicle-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #67c23a !important; /* {{ AURA: Modify - 使用完全不透明的纯绿色背景 }} */
  color: white;
  border-radius: 16px;
  position: relative;
  width: 100%;
  justify-content: center;
  border: 2px solid #ffffff !important; /* {{ AURA: Add - 添加不透明白色边框 }} */
  z-index: 3 !important; /* {{ AURA: Add - 确保层级最高 }} */
}

.vehicle-icon-compact {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vehicle-plate-compact {
  font-size: 11px;
  font-weight: 600;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn-compact {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  min-height: 12px;
  padding: 0;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #c0c4cc;
  padding: 4px;
}

.empty-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 10px;
  text-align: center;
}

.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.auto-assign-btn {
  font-size: 10px;
  padding: 2px 8px;
  height: 20px;
}

/* 时间线节点圆点 */
.timeline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: #409eff;
  border: 3px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 2; /* 圆点在最顶层 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-dot.has-vehicle {
  background: #67c23a;
  width: 16px;
  height: 16px;
  border-width: 4px;
}

/* 移除蛇形圆点样式 */

/* 移除旧的连接器样式 */

/* 移除复杂的CSS伪元素连接线系统，使用SVG替代 */

.timeline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: #409eff;
  border: 3px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-dot.has-vehicle {
  background: #67c23a;
  width: 16px;
  height: 16px;
  border-width: 4px;
}

.timeline-node:hover .timeline-dot {
  transform: translate(-50%, -50%) scale(1.2);
}

/* 优化时间序号显示 */
.time-sequence {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 3; /* 序号在所有元素之上 */
}



@media (max-width: 768px) {
  .route-schedule-container {
    padding: 0;
  }

  /* {{ AURA: Modify - 移动端合并卡片优化 }} */
  .route-and-vehicles-card {
    padding: 16px;
    margin: 8px;
    border-radius: 6px;
  }

  .selector-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .selector-left {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .selector-label {
    min-width: auto;
    justify-content: center;
  }

  /* {{ AURA: Modify - 移动端内联信息样式 }} */
  .route-info-inline {
    height: auto;
    min-height: 40px;
    flex-wrap: wrap;
    padding: 8px 12px;
    gap: 8px;
  }

  .route-info-inline .route-name,
  .route-info-inline .route-code,
  .route-info-inline .route-stats {
    font-size: 12px;
  }

  .route-info-inline .el-tag {
    font-size: 10px;
    height: 18px;
    line-height: 16px;
  }

  .vehicles-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .vehicles-grid-compact {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }

  .vehicle-item-compact {
    padding: 6px 8px;
    min-height: 40px;
  }

  .plate-number-small {
    font-size: 12px;
  }

  .timeline-grid {
    overflow-x: auto;
  }

  .timeline-header,
  .timeline-row {
    min-width: 500px;
  }

  .timeline-track {
    justify-content: center;
    gap: 12px;
    grid-template-columns: repeat(4, 140px);
  }

  .timeline-node {
    min-width: 120px;
    max-width: 140px;
    padding: 6px;
  }

  .time-text {
    font-size: 12px;
  }

  .vehicle-plate-compact {
    font-size: 10px;
  }

  /* {{ AURA: Modify - 移动端网格和连接线优化 }} */
  .timeline-track {
    grid-template-columns: repeat(4, 120px);
    gap: 15px;
  }

  .timeline-node {
    min-width: 110px;
    max-width: 120px;
    padding: 6px;
    height: 110px;
  }

  .time-text {
    font-size: 12px;
  }

  .vehicle-plate-compact {
    font-size: 10px;
    padding: 3px 6px;
  }

  .time-sequence {
    width: 18px;
    height: 18px;
    font-size: 9px;
    top: -6px;
    right: -6px;
  }

  /* 移动端连接线优化 */
  .timeline-connections {
    z-index: 2;
  }

  .connection-path {
    stroke-width: 2;
    opacity: 0.9;
  }

  .timeline-container:hover .connection-path {
    stroke-width: 3;
    opacity: 1;
  }
}

/* {{ AURA: Add - 排班确认弹窗样式 }} */
.schedule-confirm-content {
  padding: 0;
}

.confirm-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 20px 20px 0;
}

.header-icon {
  font-size: 32px;
  color: #67c23a;
}

.header-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-text p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.schedule-summary {
  background: #f8f9fa;
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.summary-item {
  text-align: center;
}

.summary-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  color: #67c23a;
}

.schedule-details {
  padding: 20px;
}

.details-header h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.assignment-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background: #ffffff;
}

.assignment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
  transition: background-color 0.2s;
  gap: 12px;
}

.assignment-item:last-child {
  border-bottom: none;
}

.assignment-item:hover {
  background-color: #f8f9fa;
}

.time-info {
  flex: 0 0 auto;
  min-width: 80px;
}

.arrow-icon {
  margin: 0;
  color: #c0c4cc;
  font-size: 16px;
  flex: 0 0 auto;
}

.vehicle-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vehicle-selection {
  display: flex;
  align-items: center;
}

.vehicle-plate {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.vehicle-trips {
  font-size: 12px;
  color: #909399;
  text-align: left;
}

.assignment-actions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.failed-assignments {
  margin-top: 20px;
  padding: 16px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
}

.failed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.failed-assignments h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #f56c6c;
}

.failed-list {
  margin-bottom: 8px;
}

.failed-list .el-tag {
  margin: 0 6px 6px 0;
}

.failed-reason {
  margin: 0;
  font-size: 12px;
  color: #f56c6c;
  font-style: italic;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
