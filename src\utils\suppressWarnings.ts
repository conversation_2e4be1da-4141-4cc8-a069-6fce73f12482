/**
 * 全局警告抑制器
 * 用于过滤掉开发环境中的无害警告，提供更清洁的控制台输出
 */

/**
 * 初始化全局警告抑制器
 * 必须在任何其他代码执行前调用
 */
export function initGlobalWarningSuppress() {
  // Firefox开发者工具的源代码映射错误无法通过console重写拦截
  // 这是因为这些错误是由开发者工具内部产生的，不经过console API
  
  // 保存原始的console方法
  const originalWarn = console.warn
  const originalError = console.error
  const originalLog = console.log
  
  // 定义需要过滤的警告关键词
  const warnFilters = [
    // WebGL相关警告
    'WEBGL_debug_renderer_info',
    'deprecated in Firefox',
    'Please use RENDERER',
    
    // 地图相关警告
    'application/octet-stream',
    'MIME type',
    '即使其 MIME 类型',
    '仍已加载来自',
    '不是有效的 JavaScript MIME 类型',
    'amap.com',
    'restapi.amap.com',
    'webapi.amap.com',
    'jsonp_',
    'maps:',
    
    // 源代码映射警告
    'source map',
    'Source map',
    'sourcemap',
    
    // 其他无害警告
    'AMap',
    'callback=___onAPILoaded'
  ]
  
  // 定义需要过滤的错误关键词（包括Firefox开发者工具特定错误）
  const errorFilters = [
    // Firefox开发者工具源代码映射错误
    '源代码映射错误',
    'Source map error',
    'URL constructor:  is not a valid URL',
    'URL constructor: is not a valid URL',
    'resolveSourceMapURL',
    'getOriginalURLs',
    'workerHandler',
    'source-map-loader',
    'resources://devtools',
    'resource://devtools',
    'fetchSourceMap.js',
    'source-map.js',
    'worker-utils.js',
    
    // WebAssembly相关错误
    'WebAssembly.Module',
    'wasm:https://webapi.amap.com',
    'wasm:http',
    
    // 地图相关错误
    'amap.com',
    'webapi.amap.com'
  ]
  
  // 检查消息是否应该被过滤
  const shouldFilter = (message: string, filters: string[]) => {
    return filters.some(filter => message.includes(filter))
  }
  
  // 重写console.warn
  console.warn = (...args) => {
    const message = args.join(' ')
    if (!shouldFilter(message, warnFilters)) {
      originalWarn.apply(console, args)
    }
  }
  
  // 重写console.error
  console.error = (...args) => {
    const message = args.join(' ')
    if (!shouldFilter(message, errorFilters)) {
      originalError.apply(console, args)
    }
  }
  
  // 全局错误处理
  window.addEventListener('error', (event) => {
    const message = event.message || ''
    const filename = event.filename || ''
    
    // 过滤地图和源代码映射相关错误
    if (
      shouldFilter(message, errorFilters) ||
      shouldFilter(filename, ['amap.com', 'webapi.amap.com']) ||
      filename.startsWith('wasm:') ||
      filename.includes('webapi.amap.com')
    ) {
      event.preventDefault()
      event.stopPropagation()
      return false
    }
  }, true)
  
  // 全局Promise拒绝处理
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason?.toString() || ''
    
    if (shouldFilter(reason, errorFilters)) {
      event.preventDefault()
      return false
    }
  })
  
  // 尝试抑制开发者工具错误（Firefox特定）
  if (navigator.userAgent.includes('Firefox')) {
    // Firefox开发者工具错误处理
    const originalConsoleError = console.error
    console.error = (...args) => {
      const message = args.join(' ')
      
      // 特别过滤Firefox开发者工具的源代码映射错误
      if (
        message.includes('URL constructor:') ||
        message.includes('resolveSourceMapURL') ||
        message.includes('source-map-loader') ||
        message.includes('workerHandler')
      ) {
        return // 完全忽略这些错误
      }
      
      originalConsoleError.apply(console, args)
    }
    
    // 尝试监听开发者工具的消息事件
    window.addEventListener('message', (event) => {
      if (event.data && typeof event.data === 'string') {
        if (shouldFilter(event.data, errorFilters)) {
          event.stopPropagation()
          event.preventDefault()
          return false
        }
      }
    }, true)
  }
  
  console.log('🔇 全局警告抑制器已初始化 (Firefox开发者工具错误无法完全屏蔽)')
}

/**
 * 恢复原始console方法（如果需要调试）
 */
export function restoreConsole() {
  // 这里可以保存原始方法的引用，然后恢复
  console.log('⚠️ 控制台已恢复原始状态')
}

/**
 * Firefox开发者工具源代码映射错误说明
 * 
 * 问题原因：
 * 1. Firefox开发者工具试图为高德地图的WebAssembly模块查找源代码映射
 * 2. WebAssembly模块的URL格式 (wasm:https://...) 对于Firefox的源代码映射解析器无效
 * 3. 这是Firefox开发者工具内部的问题，不是应用代码的问题
 * 
 * 解决方案：
 * 1. 这些错误不影响应用功能，可以安全忽略
 * 2. 我们已经尽力通过各种方式过滤这些错误
 * 3. 如果仍然显示，这是Firefox开发者工具的已知问题
 * 4. 在生产环境中不会出现这些错误
 */