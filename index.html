<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <link rel="icon" href="/favicon.ico" />
    <title>TransitSync - 智慧公交调度管理平台</title>
    <!--[if lt IE 11
      ]><script>
        window.location.href = '/html/ie.html';
      </script><!
    [endif]-->
    <style>
      html,
      body,
      #app {
        height: 100%;
        margin: 0px;
        padding: 0px;
        font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      }

      .chromeframe {
        margin: 0.2em 0;
        background: #ccc;
        color: #000;
        padding: 0.2em 0;
      }

      #loader-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999999;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .loading-container {
        text-align: center;
        color: white;
        z-index: 1001;
      }

      .brand-logo {
        margin-bottom: 40px;
        animation: fadeInDown 1s ease-out;
      }

      .logo-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
      }

      .logo-icon svg {
        width: 40px;
        height: 40px;
        fill: white;
      }

      .brand-title {
        font-size: 36px;
        font-weight: 300;
        margin: 0 0 10px 0;
        letter-spacing: 2px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }

      .brand-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0 0 60px 0;
        font-weight: 300;
      }

      /* 公交车动画 */
      .bus-animation {
        position: relative;
        width: 300px;
        height: 60px;
        margin: 0 auto 40px auto;
      }

      .bus {
        position: absolute;
        width: 60px;
        height: 30px;
        background: #fff;
        border-radius: 8px;
        left: 0;
        top: 15px;
        animation: busMove 3s ease-in-out infinite;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      }

      .bus::before {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #333;
        border-radius: 50%;
        left: 8px;
        bottom: -4px;
        animation: wheelRotate 0.5s linear infinite;
      }

      .bus::after {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #333;
        border-radius: 50%;
        right: 8px;
        bottom: -4px;
        animation: wheelRotate 0.5s linear infinite;
      }

      .bus-route {
        position: absolute;
        width: 100%;
        height: 2px;
        background: rgba(255, 255, 255, 0.3);
        bottom: 15px;
        border-radius: 1px;
      }

      .bus-stops {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
      }

      .bus-stop {
        position: absolute;
        width: 4px;
        height: 20px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 2px;
        top: 20px;
      }

      .bus-stop:nth-child(1) { left: 10%; }
      .bus-stop:nth-child(2) { left: 35%; }
      .bus-stop:nth-child(3) { left: 60%; }
      .bus-stop:nth-child(4) { left: 85%; }

      /* 加载进度条 */
      .loading-progress {
        width: 300px;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        margin: 0 auto 30px auto;
        overflow: hidden;
      }

      .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #fff, rgba(255,255,255,0.8), #fff);
        border-radius: 2px;
        animation: progressMove 2s ease-in-out infinite;
        width: 0;
      }

      /* 加载文字 */
      .loading-text {
        font-size: 18px;
        margin-bottom: 20px;
        animation: textPulse 2s ease-in-out infinite;
      }

      .loading-tips {
        font-size: 14px;
        opacity: 0.7;
        animation: fadeIn 2s ease-out;
      }

      /* 动画定义 */
      @keyframes fadeInDown {
        from {
          opacity: 0;
          transform: translateY(-50px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes busMove {
        0% {
          left: -60px;
          opacity: 0;
        }
        10% {
          opacity: 1;
        }
        90% {
          opacity: 1;
        }
        100% {
          left: 300px;
          opacity: 0;
        }
      }

      @keyframes wheelRotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      @keyframes progressMove {
        0% {
          width: 0%;
        }
        50% {
          width: 70%;
        }
        100% {
          width: 100%;
        }
      }

      @keyframes textPulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 0.7;
        }
      }

      /* 背景装饰 */
      .bg-decoration {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
      }

      .bg-decoration::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
        animation: bgMove 20s linear infinite;
      }

      @keyframes bgMove {
        from {
          transform: translateX(0) translateY(0);
        }
        to {
          transform: translateX(-60px) translateY(-60px);
        }
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .brand-title {
          font-size: 28px;
        }
        
        .brand-subtitle {
          font-size: 14px;
        }
        
        .bus-animation,
        .loading-progress {
          width: 250px;
        }
        
        .loading-text {
          font-size: 16px;
        }
      }

      /* 加载完成动画 */
      .loaded #loader-wrapper {
        opacity: 0;
        visibility: hidden;
        transition: all 0.5s ease-out;
      }

      .no-js #loader-wrapper {
        display: none;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <div id="loader-wrapper">
        <div class="bg-decoration"></div>
        <div class="loading-container">
          <!-- 品牌Logo -->
          <div class="brand-logo">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24">
                <path d="M4 16c0 .88.39 1.67 1 2.22V20c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h8v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1.78c.61-.55 1-1.34 1-2.22V6c0-3.5-3.58-4-8-4s-8 .5-8 4v10zm3.5 1c-.83 0-1.5-.67-1.5-1.5S6.67 14 7.5 14s1.5.67 1.5 1.5S8.33 17 7.5 17zm9 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm1.5-6H6V6h12v5z"/>
              </svg>
            </div>
            <h1 class="brand-title">TransitSync</h1>
            <p class="brand-subtitle">智慧公交调度管理平台</p>
          </div>

          <!-- 公交车动画 -->
          <div class="bus-animation">
            <div class="bus-route"></div>
            <div class="bus-stops">
              <div class="bus-stop"></div>
              <div class="bus-stop"></div>
              <div class="bus-stop"></div>
              <div class="bus-stop"></div>
            </div>
            <div class="bus"></div>
          </div>

          <!-- 加载进度条 -->
          <div class="loading-progress">
            <div class="progress-bar"></div>
          </div>

          <!-- 加载文字 -->
          <div class="loading-text">正在启动TransitSync系统</div>
          <div class="loading-tips">请稍候，系统正在为您准备最佳的调度体验...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
