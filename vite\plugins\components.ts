import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import IconsResolver from 'unplugin-icons/resolver';
import { VueAmapResolver } from '@vuemap/unplugin-resolver';

export default (path: any) => {
  return Components({
    resolvers: [
      // 自动导入 Element Plus 组件（排除amap相关组件）
      ElementPlusResolver({
        exclude: /^ElAmap/
      }),
      // 自动注册图标组件
      IconsResolver({
        enabledCollections: ['ep']
      }),
      // 自动导入 Vue AMap 组件
      VueAmapResolver()
    ],
    dts: path.resolve(path.resolve(__dirname, '../../src'), 'types', 'components.d.ts')
  });
};
