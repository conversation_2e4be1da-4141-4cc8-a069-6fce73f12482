<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNumber">
        <el-input
          v-model="queryParams.plateNumber"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="加油日期" prop="fuelDate">
        <el-date-picker
          v-model="queryParams.fuelDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="燃料类型" prop="fuelType">
        <el-select v-model="queryParams.fuelType" placeholder="请选择燃料类型" clearable>
          <el-option label="汽油" value="gasoline" />
          <el-option label="柴油" value="diesel" />
          <el-option label="天然气" value="cng" />
          <el-option label="电能" value="electric" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="info" plain icon="Back" @click="handleBack">返回</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fuelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="fuelId" />
      <el-table-column label="车牌号" align="center" prop="plateNumber" />
      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" />
      <el-table-column label="加油日期" align="center" prop="fuelDate" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.fuelDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="燃料类型" align="center" prop="fuelType">
        <template #default="scope">
          <el-tag v-if="scope.row.fuelType === 'gasoline'" type="success">汽油</el-tag>
          <el-tag v-else-if="scope.row.fuelType === 'diesel'" type="warning">柴油</el-tag>
          <el-tag v-else-if="scope.row.fuelType === 'cng'" type="info">天然气</el-tag>
          <el-tag v-else type="primary">电能</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="加油量" align="center" prop="fuelAmount">
        <template #default="scope">
          <span>{{ scope.row.fuelAmount }} {{ scope.row.fuelType === 'electric' ? 'kWh' : 'L' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单价(元)" align="center" prop="unitPrice" />
      <el-table-column label="总金额(元)" align="center" prop="totalAmount" />
      <el-table-column label="里程数(km)" align="center" prop="mileage" />
      <el-table-column label="加油站" align="center" prop="gasStation" />
      <el-table-column label="操作员" align="center" prop="operator" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改加油记录对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="fuelRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车辆" prop="vehicleId">
              <el-select v-model="form.vehicleId" placeholder="请选择车辆" @change="handleVehicleChange">
                <el-option
                  v-for="vehicle in vehicleOptions"
                  :key="vehicle.vehicleId"
                  :label="`${vehicle.plateNumber}(${vehicle.vehicleNumber})`"
                  :value="vehicle.vehicleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="加油日期" prop="fuelDate">
              <el-date-picker
                v-model="form.fuelDate"
                type="datetime"
                placeholder="选择加油日期"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="燃料类型" prop="fuelType">
              <el-select v-model="form.fuelType" placeholder="请选择燃料类型" @change="handleFuelTypeChange">
                <el-option label="汽油" value="gasoline" />
                <el-option label="柴油" value="diesel" />
                <el-option label="天然气" value="cng" />
                <el-option label="电能" value="electric" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="`加${fuelUnit}量`" prop="fuelAmount">
              <el-input-number v-model="form.fuelAmount" :precision="2" :min="0" @change="calculateTotal" />
              <span style="margin-left: 8px;">{{ fuelUnit }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="`单价(元/${fuelUnit})`" prop="unitPrice">
              <el-input-number v-model="form.unitPrice" :precision="2" :min="0" @change="calculateTotal" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总金额(元)" prop="totalAmount">
              <el-input-number v-model="form.totalAmount" :precision="2" :min="0" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="当前里程(km)" prop="mileage">
              <el-input-number v-model="form.mileage" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="加油站" prop="gasStation">
              <el-input v-model="form.gasStation" placeholder="请输入加油站名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VehicleFuel">
const { proxy } = getCurrentInstance();

const fuelList = ref([]);
const vehicleOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const fuelUnit = ref("L");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNumber: null,
    fuelDate: null,
    fuelType: null
  },
  rules: {
    vehicleId: [
      { required: true, message: "请选择车辆", trigger: "change" }
    ],
    fuelDate: [
      { required: true, message: "请选择加油日期", trigger: "change" }
    ],
    fuelType: [
      { required: true, message: "请选择燃料类型", trigger: "change" }
    ],
    fuelAmount: [
      { required: true, message: "请输入加油量", trigger: "blur" }
    ],
    unitPrice: [
      { required: true, message: "请输入单价", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询加油记录列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      fuelId: 1,
      vehicleId: 1,
      plateNumber: "京A12345",
      vehicleNumber: "V001",
      fuelDate: "2024-01-15 08:30:00",
      fuelType: "diesel",
      fuelAmount: 80.5,
      unitPrice: 7.85,
      totalAmount: 631.93,
      mileage: 125000,
      gasStation: "中石化加油站",
      operator: "张三",
      remark: "定期加油"
    },
    {
      fuelId: 2,
      vehicleId: 2,
      plateNumber: "京A12346",
      vehicleNumber: "V002",
      fuelDate: "2024-01-16 14:20:00",
      fuelType: "electric",
      fuelAmount: 150.0,
      unitPrice: 1.2,
      totalAmount: 180.0,
      mileage: 85000,
      gasStation: "国家电网充电站",
      operator: "李四",
      remark: "电动车充电"
    }
  ];

  setTimeout(() => {
    fuelList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

/** 获取车辆选项 */
function getVehicleOptions() {
  vehicleOptions.value = [
    { vehicleId: 1, plateNumber: "京A12345", vehicleNumber: "V001" },
    { vehicleId: 2, plateNumber: "京A12346", vehicleNumber: "V002" },
    { vehicleId: 3, plateNumber: "京A12347", vehicleNumber: "V003" }
  ];
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.fuelId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getVehicleOptions();
  open.value = true;
  title.value = "添加加油记录";
}

function handleUpdate(row) {
  reset();
  getVehicleOptions();
  form.value = { ...row };
  handleFuelTypeChange();
  open.value = true;
  title.value = "修改加油记录";
}

function handleDelete(row) {
  const fuelIds = row.fuelId || ids.value;
  proxy.$modal.confirm('是否确认删除加油记录编号为"' + fuelIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有加油记录数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function handleVehicleChange(vehicleId) {
  const vehicle = vehicleOptions.value.find(v => v.vehicleId === vehicleId);
  if (vehicle) {
    form.value.plateNumber = vehicle.plateNumber;
    form.value.vehicleNumber = vehicle.vehicleNumber;
  }
}

function handleFuelTypeChange() {
  if (form.value.fuelType === 'electric') {
    fuelUnit.value = 'kWh';
  } else {
    fuelUnit.value = 'L';
  }
}

function calculateTotal() {
  if (form.value.fuelAmount && form.value.unitPrice) {
    form.value.totalAmount = (form.value.fuelAmount * form.value.unitPrice).toFixed(2);
  }
}

function submitForm() {
  proxy.$refs["fuelRef"].validate(valid => {
    if (valid) {
      if (form.value.fuelId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function handleBack() {
  proxy.$router.push('/basic/vehicle');
}

function reset() {
  form.value = {
    fuelId: null,
    vehicleId: null,
    plateNumber: null,
    vehicleNumber: null,
    fuelDate: null,
    fuelType: null,
    fuelAmount: null,
    unitPrice: null,
    totalAmount: null,
    mileage: null,
    gasStation: null,
    operator: null,
    remark: null
  };
  fuelUnit.value = "L";
  proxy.resetForm("fuelRef");
}

getList();
</script>
