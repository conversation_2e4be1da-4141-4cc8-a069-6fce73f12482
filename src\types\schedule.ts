// 排班计划相关类型定义

/**
 * 车辆信息接口
 */
export interface Vehicle {
  /** 车辆ID */
  vehicleId: number | string
  /** 车牌号 */
  plateNumber: string
  /** 车辆编号 */
  vehicleNumber: string
  /** 车辆类型 */
  vehicleType: 'bus' | 'electric_bus' | 'hybrid_bus' | 'minibus'
  /** 品牌型号 */
  brandModel?: string
  /** 座位数 */
  seatCount?: number
  /** 发动机号 */
  engineNumber?: string
  /** 车架号 */
  vinNumber?: string
  /** 购买日期 */
  purchaseDate?: string
  /** 总里程 */
  totalMileage?: number
  /** 状态：1-正常，0-停用，2-维修中，3-故障 */
  status: '1' | '0' | '2' | '3'
  /** 是否已分配 */
  isAssigned?: boolean
  /** 分配的时间 */
  assignedTime?: string
  /** 备注 */
  remark?: string
}

/**
 * 时间点信息接口
 */
export interface TimePoint {
  /** 时间 HH:mm 格式 */
  time: string
  /** 备注 */
  remark?: string
}

/**
 * 排班项目接口
 */
export interface ScheduleItem {
  /** 唯一标识 */
  id: string | number
  /** 显示内容 */
  content: string
  /** 开始时间 */
  start: Date
  /** 结束时间（可选） */
  end?: Date
  /** 分组ID（可选） */
  group?: number | string
  /** CSS类名 */
  className?: string
  /** 车辆ID */
  vehicleId: number | string
  /** 车牌号 */
  plateNumber: string
  /** 车辆编号 */
  vehicleNumber?: string
  /** 是否可编辑 */
  editable?: boolean
  /** 样式配置 */
  style?: string
}

/**
 * 排班计划接口
 */
export interface SchedulePlan {
  /** 计划ID */
  planId?: number | string
  /** 计划名称 */
  planName: string
  /** 线路ID */
  routeId?: number | string
  /** 线路名称 */
  routeName?: string
  /** 计划日期 */
  planDate: string
  /** 计划类型：weekday-工作日，weekend-周末，holiday-节假日 */
  planType: 'weekday' | 'weekend' | 'holiday'
  /** 发车时间点列表 */
  timePoints: string[]
  /** 排班项目列表 */
  scheduleItems: ScheduleItem[]
  /** 计划状态：draft-草稿，active-生效，inactive-停用 */
  status: 'draft' | 'active' | 'inactive'
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
  /** 创建人 */
  createBy?: string
  /** 备注 */
  remark?: string
}

/**
 * 拖拽数据接口
 */
export interface DragData {
  /** 拖拽类型 */
  type: 'vehicle' | 'schedule-item'
  /** 车辆信息（当type为vehicle时） */
  vehicle?: Vehicle
  /** 排班项目信息（当type为schedule-item时） */
  scheduleItem?: ScheduleItem
  /** 额外数据 */
  extra?: Record<string, any>
}

/**
 * 时间线配置接口
 */
export interface TimelineOptions {
  /** 高度 */
  height?: string
  /** 是否可编辑 */
  editable?: boolean | {
    add?: boolean
    updateTime?: boolean
    updateGroup?: boolean
    remove?: boolean
  }
  /** 方向 */
  orientation?: 'top' | 'bottom'
  /** 是否显示当前时间 */
  showCurrentTime?: boolean
  /** 最小缩放级别（毫秒） */
  zoomMin?: number
  /** 最大缩放级别（毫秒） */
  zoomMax?: number
  /** 是否堆叠 */
  stack?: boolean
  /** 语言 */
  locale?: string
  /** 时间格式 */
  format?: {
    minorLabels?: Record<string, string>
    majorLabels?: Record<string, string>
  }
}

/**
 * 车辆状态统计接口
 */
export interface VehicleStats {
  /** 总数 */
  total: number
  /** 可用数量 */
  available: number
  /** 已分配数量 */
  assigned: number
  /** 停用数量 */
  disabled: number
  /** 维修中数量 */
  maintenance: number
  /** 故障数量 */
  fault: number
}

/**
 * 计划统计接口
 */
export interface PlanStats {
  /** 时间点数量 */
  timePointsCount: number
  /** 分配车辆数量 */
  assignedVehiclesCount: number
  /** 总班次数量 */
  totalTrips: number
  /** 时间范围 */
  timeRange: {
    start: string
    end: string
  }
}

/**
 * 冲突检测结果接口
 */
export interface ConflictResult {
  /** 是否有冲突 */
  hasConflict: boolean
  /** 冲突类型 */
  conflictType?: 'time' | 'vehicle' | 'resource'
  /** 冲突消息 */
  message?: string
  /** 冲突的项目 */
  conflictItems?: ScheduleItem[]
  /** 建议解决方案 */
  suggestions?: string[]
}

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 消息 */
  message: string
  /** 数据 */
  data: T
  /** 是否成功 */
  success: boolean
}

/**
 * 分页查询参数接口
 */
export interface PageQuery {
  /** 页码 */
  pageNum: number
  /** 页大小 */
  pageSize: number
  /** 排序字段 */
  orderBy?: string
  /** 排序方向 */
  orderDirection?: 'asc' | 'desc'
}

/**
 * 分页响应接口
 */
export interface PageResult<T = any> {
  /** 数据列表 */
  records: T[]
  /** 总数 */
  total: number
  /** 当前页 */
  current: number
  /** 页大小 */
  size: number
  /** 总页数 */
  pages: number
}

/**
 * 车辆查询参数接口
 */
export interface VehicleQuery extends PageQuery {
  /** 车牌号 */
  plateNumber?: string
  /** 车辆编号 */
  vehicleNumber?: string
  /** 车辆类型 */
  vehicleType?: string
  /** 状态 */
  status?: string
  /** 是否已分配 */
  isAssigned?: boolean
}

/**
 * 计划查询参数接口
 */
export interface PlanQuery extends PageQuery {
  /** 计划名称 */
  planName?: string
  /** 线路ID */
  routeId?: number | string
  /** 计划日期范围 */
  dateRange?: [string, string]
  /** 计划类型 */
  planType?: string
  /** 状态 */
  status?: string
}
