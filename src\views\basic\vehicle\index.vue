<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 组织机构树 -->
      <el-col :lg="4" :xs="24">
        <el-card shadow="hover">
          <el-input v-model="deptName" placeholder="请输入部门名称" prefix-icon="Search" clearable />
          <el-tree
            ref="deptTreeRef"
            class="mt-2"
            node-key="id"
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :lg="20" :xs="24">
        <el-form :model="queryParams" ref="queryFormRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNumber">
        <el-input
          v-model="queryParams.plateNumber"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆编号" prop="vehicleNumber">
        <el-input
          v-model="queryParams.vehicleNumber"
          placeholder="请输入车辆编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="1" />
          <el-option label="维修" value="2" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Document" @click="handleFuelRecord">加油记录</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Position" @click="handleDrivingRecord">行驶记录</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Tools" @click="handleMaintenanceRecord">维修保养</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 车辆卡片列表 -->
    <div v-loading="loading" class="vehicle-card-container">
      <el-row :gutter="16">
        <el-col :span="4.8" v-for="vehicle in vehicleList" :key="vehicle.vehicleId" class="mb-4">
          <el-card
            class="vehicle-card"
            :class="{ 'selected': selectedVehicles.includes(vehicle.vehicleId) }"
            shadow="hover"
            @click="toggleSelection(vehicle)"
          >
            <!-- 卡片头部 -->
            <template #header>
              <div class="card-header">
                <div class="vehicle-info">
                  <div class="plate-number">{{ vehicle.plateNumber }}</div>
                </div>
                <div class="status-badge">
                  <el-tag
                    v-if="vehicle.status === '1'"
                    type="success"
                    size="small"
                    effect="dark"
                  >正常</el-tag>
                  <el-tag
                    v-else-if="vehicle.status === '2'"
                    type="warning"
                    size="small"
                    effect="dark"
                  >维修</el-tag>
                  <el-tag
                    v-else
                    type="danger"
                    size="small"
                    effect="dark"
                  >停用</el-tag>
                </div>
              </div>
            </template>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="vehicle-image">
                <el-icon size="36" color="#409EFF">
                  <Van />
                </el-icon>
              </div>

              <div class="vehicle-details">
                <div class="detail-item">
                  <span class="label">车辆编号:</span>
                  <span class="value">{{ vehicle.vehicleNumber }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">车辆类型:</span>
                  <span class="value">{{ getVehicleTypeText(vehicle.vehicleType) }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">所属部门:</span>
                  <span class="value">{{ vehicle.deptName }}</span>
                </div>
              </div>
            </div>

            <!-- 卡片操作按钮 -->
            <template #footer>
              <div class="card-actions">
                <el-button
                  type="primary"
                  size="small"
                  icon="Edit"
                  @click.stop="handleUpdate(vehicle)"
                >修改</el-button>
                <el-button
                  type="info"
                  size="small"
                  icon="View"
                  @click.stop="handleDetail(vehicle)"
                >详情</el-button>
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click.stop="handleDelete(vehicle)"
                >删除</el-button>
              </div>
            </template>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <div v-if="!loading && vehicleList.length === 0" class="empty-state">
        <el-empty description="暂无车辆数据" />
      </div>
    </div>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改车辆对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="vehicleFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-input v-model="form.plateNumber" placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆编号" prop="vehicleNumber">
              <el-input v-model="form.vehicleNumber" placeholder="请输入车辆编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="品牌型号" prop="brandModel">
              <el-input v-model="form.brandModel" placeholder="请输入品牌型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆类型" prop="vehicleType">
              <el-select v-model="form.vehicleType" placeholder="请选择车辆类型">
                <el-option label="公交车" value="bus" />
                <el-option label="电动公交" value="electric_bus" />
                <el-option label="混合动力" value="hybrid_bus" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="座位数" prop="seatCount">
              <el-input-number v-model="form.seatCount" :min="10" :max="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发动机号" prop="engineNumber">
              <el-input v-model="form.engineNumber" placeholder="请输入发动机号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="车架号" prop="vinNumber">
              <el-input v-model="form.vinNumber" placeholder="请输入车架号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="购买日期" prop="purchaseDate">
              <el-date-picker
                v-model="form.purchaseDate"
                type="date"
                placeholder="选择购买日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ label: 'label', children: 'children', value: 'id' }"
                placeholder="请选择所属部门"
                check-strictly
                :render-after-expand="false"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总里程(km)" prop="totalMileage">
              <el-input-number v-model="form.totalMileage" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="正常" value="1" />
                <el-option label="维修" value="2" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Vehicle">
import { listVehicle, getVehicle, delVehicle, addVehicle, updateVehicle } from "@/api/basic/vehicle";
import { deptTreeSelect } from "@/api/system/user";
import { Van } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const vehicleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 卡片选择相关
const selectedVehicles = ref([]);

// 部门树相关数据
const deptName = ref('');
const deptOptions = ref([]);
const deptTreeRef = ref();

// 表单引用
const queryFormRef = ref();
const vehicleFormRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 25,
    plateNumber: null,
    vehicleNumber: null,
    status: null,
    deptId: null
  },
  rules: {
    plateNumber: [
      { required: true, message: "车牌号不能为空", trigger: "blur" }
    ],
    vehicleNumber: [
      { required: true, message: "车辆编号不能为空", trigger: "blur" }
    ],
    brandModel: [
      { required: true, message: "品牌型号不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 部门树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选部门树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询部门下拉树结构 */
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取部门树失败:', error);
    // 模拟部门数据
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' }
            ]
          },
          {
            id: 5,
            label: '维修部',
            children: [
              { id: 6, label: '机修组' },
              { id: 7, label: '电修组' }
            ]
          }
        ]
      }
    ];
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 查询车辆列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      vehicleId: 1,
      plateNumber: "京A12345",
      vehicleNumber: "V001",
      brandModel: "宇通ZK6105HNG",
      vehicleType: "bus",
      seatCount: 35,
      engineNumber: "YC6G260N-50",
      vinNumber: "LZYTBH1234567890",
      purchaseDate: "2020-03-15",
      totalMileage: 125000,
      deptId: 3,
      deptName: "第一车队",
      status: "1",
      remark: "1路公交车"
    },
    {
      vehicleId: 2,
      plateNumber: "京A12346",
      vehicleNumber: "V002",
      brandModel: "比亚迪K9",
      vehicleType: "electric_bus",
      seatCount: 30,
      engineNumber: "BYD-E001",
      vinNumber: "LBYDEH1234567890",
      purchaseDate: "2021-06-20",
      totalMileage: 85000,
      deptId: 4,
      deptName: "第二车队",
      status: "1",
      remark: "2路电动公交车"
    },
    {
      vehicleId: 3,
      plateNumber: "京A12347",
      vehicleNumber: "V003",
      brandModel: "金龙XMQ6127",
      vehicleType: "bus",
      seatCount: 40,
      engineNumber: "WP7.270E51",
      vinNumber: "LKJLBH1234567890",
      purchaseDate: "2019-12-10",
      totalMileage: 180000,
      deptId: 6,
      deptName: "机修组",
      status: "2",
      remark: "3路公交车，正在维修"
    },
    {
      vehicleId: 4,
      plateNumber: "京A12348",
      vehicleNumber: "V004",
      brandModel: "福田欧辉BJ6105",
      vehicleType: "electric_bus",
      seatCount: 32,
      engineNumber: "FT-E002",
      vinNumber: "LFTBH2345678901",
      purchaseDate: "2022-01-10",
      totalMileage: 45000,
      deptId: 3,
      deptName: "第一车队",
      status: "1",
      remark: "4路电动公交车"
    },
    {
      vehicleId: 5,
      plateNumber: "京A12349",
      vehicleNumber: "V005",
      brandModel: "中通LCK6108",
      vehicleType: "hybrid_bus",
      seatCount: 38,
      engineNumber: "ZT-H001",
      vinNumber: "LZTBH3456789012",
      purchaseDate: "2021-09-15",
      totalMileage: 72000,
      deptId: 4,
      deptName: "第二车队",
      status: "1",
      remark: "5路混合动力公交车"
    },
    {
      vehicleId: 6,
      plateNumber: "京A12350",
      vehicleNumber: "V006",
      brandModel: "安凯HFF6120",
      vehicleType: "bus",
      seatCount: 42,
      engineNumber: "AK6G280N-60",
      vinNumber: "LAKBH4567890123",
      purchaseDate: "2020-11-25",
      totalMileage: 98000,
      deptId: 3,
      deptName: "第一车队",
      status: "0",
      remark: "6路公交车，已停用"
    },
    {
      vehicleId: 7,
      plateNumber: "京A12351",
      vehicleNumber: "V007",
      brandModel: "海格KLQ6129",
      vehicleType: "electric_bus",
      seatCount: 36,
      engineNumber: "HG-E003",
      vinNumber: "LHGBH5678901234",
      purchaseDate: "2022-05-08",
      totalMileage: 28000,
      deptId: 4,
      deptName: "第二车队",
      status: "1",
      remark: "7路电动公交车"
    },
    {
      vehicleId: 8,
      plateNumber: "京A12352",
      vehicleNumber: "V008",
      brandModel: "申龙SLK6109",
      vehicleType: "bus",
      seatCount: 34,
      engineNumber: "SL6G270N-55",
      vinNumber: "LSLBH6789012345",
      purchaseDate: "2019-08-12",
      totalMileage: 156000,
      deptId: 6,
      deptName: "机修组",
      status: "2",
      remark: "8路公交车，维修中"
    },
    {
      vehicleId: 9,
      plateNumber: "京A12353",
      vehicleNumber: "V009",
      brandModel: "银隆GTQ6105",
      vehicleType: "electric_bus",
      seatCount: 31,
      engineNumber: "YL-E004",
      vinNumber: "LYLBH7890123456",
      purchaseDate: "2022-03-22",
      totalMileage: 35000,
      deptId: 3,
      deptName: "第一车队",
      status: "1",
      remark: "9路电动公交车"
    },
    {
      vehicleId: 10,
      plateNumber: "京A12354",
      vehicleNumber: "V010",
      brandModel: "东风超龙EQ6100",
      vehicleType: "hybrid_bus",
      seatCount: 37,
      engineNumber: "DF-H002",
      vinNumber: "LDFBH8901234567",
      purchaseDate: "2021-12-05",
      totalMileage: 58000,
      deptId: 4,
      deptName: "第二车队",
      status: "1",
      remark: "10路混合动力公交车"
    },
    {
      vehicleId: 11,
      plateNumber: "京A12355",
      vehicleNumber: "V011",
      brandModel: "青年JNP6120",
      vehicleType: "bus",
      seatCount: 41,
      engineNumber: "QN6G285N-65",
      vinNumber: "LQNBH9012345678",
      purchaseDate: "2020-07-18",
      totalMileage: 112000,
      deptId: 7,
      deptName: "电修组",
      status: "1",
      remark: "11路公交车"
    },
    {
      vehicleId: 12,
      plateNumber: "京A12356",
      vehicleNumber: "V012",
      brandModel: "北汽福田BJ6123",
      vehicleType: "electric_bus",
      seatCount: 39,
      engineNumber: "BQ-E005",
      vinNumber: "LBQBH0123456789",
      purchaseDate: "2022-08-30",
      totalMileage: 18000,
      deptId: 3,
      deptName: "第一车队",
      status: "1",
      remark: "12路电动公交车"
    }
  ];
  
  setTimeout(() => {
    let filteredData = mockData;

    // 根据部门筛选
    if (queryParams.value.deptId) {
      filteredData = filteredData.filter(item => item.deptId === queryParams.value.deptId);
    }

    // 根据车牌号筛选
    if (queryParams.value.plateNumber) {
      filteredData = filteredData.filter(item =>
        item.plateNumber.includes(queryParams.value.plateNumber)
      );
    }

    // 根据车辆编号筛选
    if (queryParams.value.vehicleNumber) {
      filteredData = filteredData.filter(item =>
        item.vehicleNumber.includes(queryParams.value.vehicleNumber)
      );
    }

    // 根据状态筛选
    if (queryParams.value.status) {
      filteredData = filteredData.filter(item => item.status === queryParams.value.status);
    }

    vehicleList.value = filteredData;
    total.value = filteredData.length;
    loading.value = false;
  }, 500);
}

// 其他方法实现...
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.vehicleId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 卡片选择切换 */
function toggleSelection(vehicle) {
  const index = selectedVehicles.value.indexOf(vehicle.vehicleId);
  if (index > -1) {
    selectedVehicles.value.splice(index, 1);
  } else {
    selectedVehicles.value.push(vehicle.vehicleId);
  }

  // 更新选择状态
  ids.value = selectedVehicles.value;
  single.value = selectedVehicles.value.length !== 1;
  multiple.value = selectedVehicles.value.length === 0;
}

/** 获取车辆类型文本 */
function getVehicleTypeText(type) {
  const typeMap = {
    'bus': '公交车',
    'electric_bus': '电动公交',
    'hybrid_bus': '混合动力'
  };
  return typeMap[type] || type;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加车辆";
}

function handleUpdate(row) {
  reset();
  form.value = { ...row };
  open.value = true;
  title.value = "修改车辆";
}

function handleDetail(row) {
  proxy.$router.push({
    path: '/basic/vehicle/detail',
    query: { vehicleId: row.vehicleId }
  });
}

function handleDelete(row) {
  const vehicleIds = row.vehicleId || ids.value;
  proxy.$modal.confirm('是否确认删除车辆编号为"' + vehicleIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleFuelRecord() {
  proxy.$router.push('/basic/vehicle/fuel');
}

function handleDrivingRecord() {
  proxy.$router.push('/basic/vehicle/driving');
}

function handleMaintenanceRecord() {
  proxy.$router.push('/basic/vehicle/maintenance');
}

function submitForm() {
  vehicleFormRef.value?.validate(valid => {
    if (valid) {
      if (form.value.vehicleId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    vehicleId: null,
    plateNumber: null,
    vehicleNumber: null,
    brandModel: null,
    vehicleType: null,
    seatCount: null,
    engineNumber: null,
    vinNumber: null,
    purchaseDate: null,
    totalMileage: 0,
    deptId: null,
    status: "1",
    remark: null
  };
  vehicleFormRef.value?.resetFields();
  vehicleFormRef.value?.clearValidate();
}

getList();
getTreeSelect();
</script>

<style scoped>
.vehicle-card-container {
  min-height: 400px;
}

.vehicle-card {
  height: 240px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.vehicle-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.vehicle-card.selected {
  border: 2px solid #409EFF;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0;
}

.vehicle-info {
  flex: 1;
}

.plate-number {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.vehicle-number {
  font-size: 12px;
  color: #909399;
}

.status-badge {
  margin-left: 8px;
  margin-top: -2px;
}

.card-content {
  padding: 0;
  height: 120px;
  display: flex;
  flex-direction: column;
}

.vehicle-image {
  text-align: center;
  padding: 8px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  margin: -8px -16px 8px -16px;
}

.vehicle-details {
  flex: 1;
  padding: 0 4px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 13px;
}

.detail-item .label {
  color: #909399;
  font-weight: 500;
  min-width: 60px;
}

.detail-item .value {
  color: #303133;
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  gap: 6px;
  padding: 0;
  min-height: 32px;
  align-items: center;
  width: 100%;
}

.card-actions .el-button {
  flex: 1;
  margin: 0;
  font-size: 11px;
  padding: 4px 8px;
}

/* 确保footer区域显示 */
.vehicle-card :deep(.el-card__body) {
  flex: 1;
  padding: 16px;
}

.vehicle-card :deep(.el-card__footer) {
  padding: 8px 16px;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  margin-top: auto;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.mb-4 {
  margin-bottom: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .vehicle-card-container .el-col {
    width: 20%; /* 一行4个 */
  }
}

@media (max-width: 992px) {
  .vehicle-card-container .el-col {
    width: 33.333%; /* 一行3个 */
  }
}

@media (max-width: 768px) {
  .vehicle-card-container .el-col {
    width: 50%; /* 一行2个 */
  }
}

@media (max-width: 576px) {
  .vehicle-card-container .el-col {
    width: 100%; /* 一行1个 */
  }
}
</style>
