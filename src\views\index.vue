<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="24" style="padding-left: 20px">
        <h2>TransitSync 智慧公交调度管理平台</h2>
        <p>
          TransitSync 是一个现代化的公交调度管理系统，专注于提升公交运营效率和服务质量
          <br />
          <br />
          <strong>核心功能模块：</strong><br />
          * 线路管理 - 公交线路的规划、配置和优化管理<br />
          * 车辆管理 - 公交车辆的信息管理和状态监控<br />
          * 司机管理 - 司机档案管理和工作安排<br />
          * 调度管理 - 智能排班和实时调度优化<br />
          * 实时监控 - 车辆位置、运行状态的实时跟踪<br />
          * 数据分析 - 运营数据统计和决策支持<br />
          * 乘客服务 - 线路查询和服务信息发布<br />
          * 系统管理 - 用户权限和系统配置管理<br />
          <br />
          <strong>技术特色：</strong><br />
          * 现代化前端架构 Vue3 + TypeScript + Element Plus<br />
          * 响应式设计，支持多端访问<br />
          * 实时数据更新和智能调度算法<br />
          * 可视化数据展示和报表生成<br />
          * 高性能和高可用性系统架构<br />
        </p>
        <p><b>当前版本:</b> <span>v1.0.0</span></p>
        <p>
          <el-tag type="success">智慧调度</el-tag>
          <el-tag type="primary" style="margin-left: 10px">实时监控</el-tag>
          <el-tag type="warning" style="margin-left: 10px">数据分析</el-tag>
        </p>
      </el-col>
    </el-row>
    <el-divider />
    
    <el-row :gutter="20">
      <el-col :sm="24" :lg="8">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <svg-icon icon-class="system" class="card-icon" />
              <span>线路管理</span>
            </div>
          </template>
          <p>完善的公交线路管理功能，支持线路规划、站点配置、运营时间设置等。</p>
        </el-card>
      </el-col>
      <el-col :sm="24" :lg="8">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <svg-icon icon-class="monitor" class="card-icon" />
              <span>实时监控</span>
            </div>
          </template>
          <p>实时跟踪车辆位置和运行状态，提供可视化的监控界面。</p>
        </el-card>
      </el-col>
      <el-col :sm="24" :lg="8">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <svg-icon icon-class="chart" class="card-icon" />
              <span>数据分析</span>
            </div>
          </template>
          <p>智能数据分析和报表生成，为运营决策提供数据支持。</p>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index" lang="ts">
// 首页组件 - TransitSync智慧公交调度管理平台
</script>

<style lang="scss" scoped>
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }
}

.feature-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .card-header {
    display: flex;
    align-items: center;
    
    .card-icon {
      font-size: 20px;
      margin-right: 8px;
      color: #409eff;
    }
  }
}
</style>
