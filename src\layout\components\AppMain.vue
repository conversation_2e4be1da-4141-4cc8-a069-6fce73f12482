<template>
  <section ref="appMainRef" class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition :enter-active-class="animate" mode="out-in">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component :is="Component" v-if="!route.meta.link" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup name="AppMain" lang="ts">
import { useSettingsStore } from '@/store/modules/settings';
import { useTagsViewStore } from '@/store/modules/tagsView';
import { useScrollbar } from '@/composables/useScrollbar';

import IframeToggle from './IframeToggle/index.vue';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const route = useRoute();
const tagsViewStore = useTagsViewStore();

// 应用主区域引用和滚动条
const appMainRef = ref<HTMLElement>()
const { initializeScrollbar } = useScrollbar(appMainRef)

// 简洁的页面切换动画 - 适合公交调度平台
const animate = ref<string>(proxy?.animate.pageTransitionAnimate || 'animate__animated animate__fadeIn');
const animationEnable = ref(useSettingsStore().animationEnable);

// 监听动画设置变化 - 始终使用简洁的淡入效果
watch(
  () => useSettingsStore().animationEnable,
  (val: boolean) => {
    animationEnable.value = val;
    if (val) {
      // 启用动画时使用专业的页面切换动画
      animate.value = proxy?.animate.pageTransitionAnimate || 'animate__animated animate__fadeIn';
    } else {
      // 禁用动画时不使用任何动画类
      animate.value = '';
    }
  },
  { immediate: true }
);

onMounted(() => {
  addIframe();
  // 初始化滚动条
  nextTick(() => {
    if (appMainRef.value) {
      initializeScrollbar()
    }
  })
});

watchEffect(() => {
  addIframe();
});

function addIframe() {
  if (route.meta.link) {
    useTagsViewStore().addIframeView(route);
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  // {{ AURA-X: Modify - 移除 overflow: hidden，让 OverlayScrollbars 接管滚动 }}
  // overflow: hidden; // 由 OverlayScrollbars 接管
  height: calc(100vh - 50px);
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
    height: calc(100vh - 84px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>
<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

// {{ AURA-X: Remove - 移除旧的滚动条样式，使用全局统一的 OverlayScrollbars 样式 }}
// 旧的 webkit 滚动条样式已移除，现在使用 src/assets/styles/scrollbar.scss 中的统一样式
</style>
