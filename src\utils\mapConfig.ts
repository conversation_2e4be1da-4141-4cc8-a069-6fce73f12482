/**
 * 高德地图配置工具
 * 用于减少浏览器警告和优化地图性能
 */
import { ElMessage } from 'element-plus'

// 地图默认配置
export const DEFAULT_MAP_CONFIG = {
  zoom: 13,
  center: [116.397428, 39.90923] as [number, number],
  mapStyle: 'amap://styles/normal',
  // 禁用一些可能产生警告的功能
  features: ['bg', 'road', 'building', 'point'],
  // 优化渲染性能
  viewMode: '2D' as const,
  // 减少网络请求
  cacheSize: 256,
  // 禁用调试信息
  debug: false
}

// 控件配置
export const CONTROL_CONFIG = {
  scale: {
    position: 'LB' as const,
    visible: true
  },
  toolBar: {
    position: 'RB' as const,
    visible: true,
    // 减少工具栏功能以避免警告
    locate: false,
    noIpLocate: true,
    liteStyle: true
  }
}

// 标记点配置
export const MARKER_CONFIG = {
  // 使用简单的标记样式避免图标加载警告
  anchor: 'bottom-center' as const,
  draggable: false,
  bubble: true
}

// 路线配置
export const POLYLINE_CONFIG = {
  strokeWeight: 4,
  strokeOpacity: 0.8,
  strokeStyle: 'solid' as const,
  // 减少路线复杂度
  geodesic: false,
  isOutline: false
}

/**
 * 创建优化的地图实例
 * @param containerId 地图容器ID
 * @param config 自定义配置
 * @returns 地图实例
 */
export function createOptimizedMap(containerId: string, config: Partial<typeof DEFAULT_MAP_CONFIG> = {}) {
  const finalConfig = { ...DEFAULT_MAP_CONFIG, ...config }
  
  try {
    const map = new AMap.Map(containerId, finalConfig)
    
    // 禁用一些可能产生警告的事件
    map.setStatus({
      dragEnable: true,
      zoomEnable: true,
      doubleClickZoom: true,
      keyboardEnable: false, // 禁用键盘控制减少警告
      jogEnable: false,      // 禁用惯性拖拽
      animateEnable: true
    })
    
    return map
  } catch (error) {
    console.error('地图创建失败:', error)
    throw error
  }
}

/**
 * 添加优化的控件
 * @param map 地图实例
 */
export function addOptimizedControls(map: any) {
  AMap.plugin(['AMap.Scale', 'AMap.ToolBar'], () => {
    try {
      // 添加比例尺控件
      const scale = new AMap.Scale(CONTROL_CONFIG.scale)
      map.addControl(scale)

      // 添加工具栏控件
      const toolBar = new AMap.ToolBar(CONTROL_CONFIG.toolBar)
      map.addControl(toolBar)
    } catch (error) {
      console.warn('控件添加失败:', error)
    }
  })
}

/**
 * 创建优化的标记点
 * @param position 位置
 * @param title 标题
 * @param config 自定义配置
 * @returns 标记点实例
 */
export function createOptimizedMarker(
  position: [number, number], 
  title: string, 
  config: Partial<typeof MARKER_CONFIG> = {}
) {
  const finalConfig = { 
    ...MARKER_CONFIG, 
    ...config, 
    position, 
    title 
  }
  
  return new AMap.Marker(finalConfig)
}

/**
 * 创建优化的路线
 * @param path 路径点数组
 * @param color 颜色
 * @param config 自定义配置
 * @returns 路线实例
 */
export function createOptimizedPolyline(
  path: [number, number][], 
  color: string = '#1890ff',
  config: Partial<typeof POLYLINE_CONFIG> = {}
) {
  const finalConfig = { 
    ...POLYLINE_CONFIG, 
    ...config, 
    path, 
    strokeColor: color 
  }
  
  return new AMap.Polyline(finalConfig)
}

/**
 * 抑制常见的浏览器警告和地图相关警告
 */
export function suppressMapWarnings() {
  // 抑制WebGL和地图相关警告
  const originalWarn = console.warn
  const originalError = console.error
  
  console.warn = (...args) => {
    const message = args.join(' ')
    
    // 过滤掉已知的无害警告
    if (
      message.includes('WEBGL_debug_renderer_info') ||
      message.includes('application/octet-stream') ||
      message.includes('source map') ||
      message.includes('Source map') ||
      message.includes('MIME type') ||
      message.includes('即使其 MIME 类型') ||
      message.includes('仍已加载来自') ||
      message.includes('不是有效的 JavaScript MIME 类型') ||
      message.includes('AMap') ||
      message.includes('amap.com') ||
      message.includes('restapi.amap.com') ||
      message.includes('webapi.amap.com') ||
      message.includes('jsonp_') ||
      message.includes('maps:') ||
      message.includes('RENDERER') ||
      message.includes('deprecated in Firefox') ||
      message.includes('Please use RENDERER')
    ) {
      return // 不显示这些警告
    }
    
    originalWarn.apply(console, args)
  }
  
  console.error = (...args) => {
    const message = args.join(' ')
    
    // 过滤掉源代码映射相关错误和地图相关错误
    if (
      message.includes('源代码映射错误') ||
      message.includes('Source map error') ||
      message.includes('URL constructor:  is not a valid URL') ||
      message.includes('WebAssembly.Module') ||
      message.includes('wasm:https://webapi.amap.com') ||
      message.includes('resolveSourceMapURL') ||
      message.includes('getOriginalURLs') ||
      message.includes('workerHandler') ||
      message.includes('source-map-loader') ||
      message.includes('resources://devtools')
    ) {
      return // 不显示这些错误
    }
    
    originalError.apply(console, args)
  }
  
  // 监听window错误事件，过滤地图相关错误
  window.addEventListener('error', (event) => {
    if (
      event.message?.includes('amap.com') ||
      event.message?.includes('webapi.amap.com') ||
      event.message?.includes('Source map') ||
      event.filename?.includes('amap.com') ||
      event.filename?.includes('webapi.amap.com')
    ) {
      event.preventDefault()
      event.stopPropagation()
      return false
    }
  }, true)
  
  // 监听未处理的Promise拒绝，过滤地图相关错误
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason?.toString() || ''
    if (
      reason.includes('amap.com') ||
      reason.includes('webapi.amap.com') ||
      reason.includes('Source map') ||
      reason.includes('URL constructor')
    ) {
      event.preventDefault()
      return false
    }
  })
}

/**
 * 地图错误处理
 */
export function handleMapError(error: any, context: string = '地图操作') {
  console.error(`${context}失败:`, error)
  
  // 根据错误类型提供不同的用户提示
  if (error.message?.includes('network') || error.message?.includes('timeout')) {
    ElMessage.error('网络连接异常，请检查网络后重试')
  } else if (error.message?.includes('key') || error.message?.includes('security')) {
    ElMessage.error('地图服务配置异常，请联系管理员')
  } else {
    ElMessage.error(`${context}失败，请刷新页面重试`)
  }
}
