<template>
  <div class="action-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">
        <svg-icon icon-class="tool" />
        <span>{{ subMenus.length > 0 ? '菜单导航' : '快捷操作' }}</span>
      </div>
      <el-button 
        type="text" 
        @click="$emit('togglePanel')"
        class="toggle-btn"
      >
        <el-icon><DArrowLeft /></el-icon>
      </el-button>
    </div>
    
    <!-- 子菜单导航（如果有的话） -->
    <div v-if="subMenus.length > 0" class="sub-menu-section">
      <div class="section-title">
        <svg-icon icon-class="menu" />
        <span>子菜单 ({{ subMenus.length }})</span>
      </div>
      <div class="sub-menu-items">
        <router-link 
          v-for="item in subMenus" 
          :key="item.path"
          :to="item.path"
          class="sub-menu-item"
          :class="{ 'active': $route.path === item.path }"
        >
          <svg-icon v-if="item.meta && item.meta.icon" :icon-class="item.meta.icon" />
          <span>{{ item.meta?.title || item.name }}</span>
        </router-link>
      </div>
    </div>
    
    <!-- 调试信息（开发环境下显示） -->
    <div v-if="isDev" class="debug-info">
      <div class="section-title">调试信息</div>
      <div class="debug-content">
        <p>当前路由: {{ currentRoute }}</p>
        <p>子菜单数量: {{ subMenus.length }}</p>
        <p>侧边栏路由: {{ JSON.stringify(subMenus.map(m => ({ path: m.path, title: m.meta?.title })), null, 2) }}</p>
      </div>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="action-buttons" v-if="currentPageActions.length > 0">
      <template v-for="action in currentPageActions" :key="action.key">
        <el-button 
          v-has-permi="action.permissions"
          :type="action.type"
          :icon="action.icon"
          @click="handleAction(action)"
          class="action-btn"
        >
          {{ action.label }}
        </el-button>
      </template>
    </div>
    
    <!-- 统计卡片区域 -->
    <div v-if="currentPageStats && currentPageStats.length > 0" class="stats-section">
      <div class="section-title">
        <svg-icon icon-class="chart" />
        <span>数据概览</span>
      </div>
      <div class="stats-cards">
        <div 
          v-for="stat in currentPageStats" 
          :key="stat.key"
          class="stat-card"
          :class="stat.type"
        >
          <div class="stat-icon">
            <svg-icon :icon-class="stat.icon" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快速导航 -->
    <div v-if="quickNavItems.length > 0" class="quick-nav-section">
      <div class="section-title">
        <svg-icon icon-class="link" />
        <span>快速导航</span>
      </div>
      <div class="quick-nav-items">
        <router-link 
          v-for="item in quickNavItems" 
          :key="item.path"
          :to="item.path"
          class="nav-item"
          v-has-permi="item.permissions"
        >
          <svg-icon :icon-class="item.icon" />
          <span>{{ item.title }}</span>
        </router-link>
      </div>
    </div>
    
    <!-- 最近访问 -->
    <div v-if="recentVisits.length > 0" class="recent-section">
      <div class="section-title">
        <svg-icon icon-class="time" />
        <span>最近访问</span>
      </div>
      <div class="recent-items">
        <router-link 
          v-for="item in recentVisits" 
          :key="item.path"
          :to="item.path"
          class="recent-item"
        >
          <svg-icon :icon-class="item.icon || 'document'" />
          <span>{{ item.title }}</span>
          <small>{{ formatTime(item.visitTime) }}</small>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePageActions } from '@/composables/usePageActions'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { usePermissionStore } from '@/store/modules/permission'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

interface ActionItem {
  key: string
  label: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon: string
  permissions: string[]
  handler?: () => void
}

interface StatItem {
  key: string
  label: string
  value: string | number
  icon: string
  type: 'primary' | 'success' | 'warning' | 'danger'
}

interface NavItem {
  path: string
  title: string
  icon: string
  permissions: string[]
}

const props = defineProps<{
  currentRoute: string
}>()

const emit = defineEmits<{
  togglePanel: []
}>()

const { getPageActions, getPageStats } = usePageActions()
const tagsViewStore = useTagsViewStore()
const permissionStore = usePermissionStore()

// 当前页面操作按钮
const currentPageActions = ref<ActionItem[]>([])

// 当前页面统计数据
const currentPageStats = ref<StatItem[]>([])

// 子菜单（从权限存储获取侧边栏路由）
const subMenus = computed(() => {
  return permissionStore.getSidebarRoutes().filter(route => !route.hidden)
})

// 开发环境标识
const isDev = computed(() => import.meta.env.DEV)

// 快速导航项
const quickNavItems = computed<NavItem[]>(() => {
  const route = props.currentRoute
  const quickNavConfig: Record<string, NavItem[]> = {
    '/vehicle': [
      { path: '/vehicle/add', title: '新增车辆', icon: 'plus', permissions: ['vehicle:add'] },
      { path: '/vehicle/import', title: '批量导入', icon: 'upload', permissions: ['vehicle:import'] },
      { path: '/vehicle/maintenance', title: '维护记录', icon: 'tool', permissions: ['vehicle:maintenance'] }
    ],
    '/driver': [
      { path: '/driver/add', title: '新增司机', icon: 'plus', permissions: ['driver:add'] },
      { path: '/driver/schedule', title: '排班管理', icon: 'schedule', permissions: ['driver:schedule'] }
    ],
    '/route': [
      { path: '/route/add', title: '新增路线', icon: 'plus', permissions: ['route:add'] },
      { path: '/route/map', title: '路线地图', icon: 'location', permissions: ['route:view'] }
    ]
  }
  
  const baseRoute = '/' + route.split('/')[1]
  return quickNavConfig[baseRoute] || []
})

// 最近访问
const recentVisits = computed(() => {
  return tagsViewStore.visitedViews
    .filter(view => view.path !== props.currentRoute)
    .slice(0, 5)
    .map(view => ({
      ...view,
      visitTime: Date.now() - Math.random() * 86400000 // 模拟访问时间
    }))
})

// 处理操作按钮点击
const handleAction = (action: ActionItem) => {
  if (action.handler) {
    action.handler()
  } else {
    // 默认处理
    ElMessage.info(`执行操作: ${action.label}`)
  }
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return dayjs(timestamp).fromNow()
}

// 加载页面数据
const loadPageData = async () => {
  try {
    // 加载操作按钮
    currentPageActions.value = await getPageActions(props.currentRoute)
    
    // 加载统计数据
    currentPageStats.value = await getPageStats(props.currentRoute)
  } catch (error) {
    console.error('加载页面数据失败:', error)
  }
}

// 监听路由变化
watch(() => props.currentRoute, () => {
  loadPageData()
}, { immediate: true })
</script>

<style lang="scss" scoped>
.action-panel {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #333;
      
      .svg-icon {
        font-size: 16px;
      }
    }
    
    .toggle-btn {
      padding: 5px;
      
      &:hover {
        background-color: #f0f0f0;
      }
    }
  }
  
  .action-buttons {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .action-btn {
      width: 100%;
      margin-bottom: 10px;
      justify-content: flex-start;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .sub-menu-section,
  .stats-section,
  .quick-nav-section,
  .recent-section,
  .debug-info {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
      
      .svg-icon {
        font-size: 14px;
      }
    }
  }
  
  .debug-info {
    background: #f9f9f9;
    
    .debug-content {
      font-family: monospace;
      font-size: 11px;
      color: #666;
      
      p {
        margin: 5px 0;
        word-break: break-all;
      }
      
      pre {
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
  
  .sub-menu-items {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .sub-menu-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 12px;
      border-radius: 4px;
      color: #333;
      text-decoration: none;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f0f0f0;
        color: #1890ff;
      }
      
      &.active {
        background-color: #e6f7ff;
        color: #1890ff;
        border-left: 3px solid #1890ff;
      }
      
      .svg-icon {
        font-size: 14px;
      }
    }
  }
  
  .stats-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .stat-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      border-radius: 6px;
      background-color: #f8f9fa;
      
      &.primary {
        background-color: #e3f2fd;
        
        .stat-icon {
          color: #1976d2;
        }
      }
      
      &.success {
        background-color: #e8f5e8;
        
        .stat-icon {
          color: #388e3c;
        }
      }
      
      &.warning {
        background-color: #fff3e0;
        
        .stat-icon {
          color: #f57c00;
        }
      }
      
      &.danger {
        background-color: #ffebee;
        
        .stat-icon {
          color: #d32f2f;
        }
      }
      
      .stat-icon {
        font-size: 20px;
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 18px;
          font-weight: bold;
          color: #333;
        }
        
        .stat-label {
          font-size: 12px;
          color: #666;
          margin-top: 2px;
        }
      }
    }
  }
  
  .quick-nav-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .nav-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 12px;
      border-radius: 4px;
      color: #333;
      text-decoration: none;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f0f0f0;
        color: #1890ff;
      }
      
      .svg-icon {
        font-size: 14px;
      }
    }
  }
  
  .recent-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .recent-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px 12px;
      border-radius: 4px;
      color: #333;
      text-decoration: none;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f0f0f0;
      }
      
      .svg-icon {
        font-size: 14px;
        color: #666;
      }
      
      span {
        flex: 1;
        font-size: 13px;
      }
      
      small {
        font-size: 11px;
        color: #999;
      }
    }
  }
}

// 滚动条样式
.action-panel::-webkit-scrollbar {
  width: 4px;
}

.action-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.action-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.action-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>