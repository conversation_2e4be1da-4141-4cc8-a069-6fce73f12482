# 司机管理页面重置功能测试

## 重置功能对比

### 修改前（旧方式）
```javascript
// 查询重置
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 表单重置
function reset() {
  form.value = { /* 手动设置默认值 */ };
  //proxy.resetForm("driverRef"); // 注释掉的代码
}
```

### 修改后（标准方式）
```javascript
// 查询重置 - 参考 system/user/index.vue
function resetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 表单重置 - 参考 system/user/index.vue
function reset() {
  form.value = { /* 设置默认值 */ };
  driverFormRef.value?.resetFields();
  driverFormRef.value?.clearValidate();
}
```

## 表单引用更新

### 模板中的引用
```vue
<!-- 查询表单 -->
<el-form :model="queryParams" ref="queryFormRef" :inline="true">
  <!-- 表单项 -->
</el-form>

<!-- 司机表单 -->
<el-form ref="driverFormRef" :model="form" :rules="rules" label-width="80px">
  <!-- 表单项 -->
</el-form>
```

### 脚本中的引用定义
```javascript
// 表单引用
const queryFormRef = ref();
const driverFormRef = ref();
```

## 表单验证更新

### 提交表单验证
```javascript
function submitForm() {
  driverFormRef.value?.validate(valid => {
    if (valid) {
      // 提交逻辑
    }
  });
}
```

## 重置功能详解

### 1. 查询重置功能
- **resetFields()**: 重置表单字段到初始值
- **pageNum = 1**: 重置分页到第一页
- **deptId = null**: 清空部门筛选
- **setCurrentKey(null)**: 清空部门树选中状态
- **handleQuery()**: 重新执行查询

### 2. 表单重置功能
- **form.value = {...}**: 手动设置表单默认值
- **resetFields()**: 重置表单字段验证状态
- **clearValidate()**: 清除表单验证错误信息

## 测试场景

### 场景1: 查询重置测试
1. 在搜索条件中输入司机姓名、工号等
2. 选择状态筛选条件
3. 点击左侧部门树选择部门
4. 点击"重置"按钮
5. 验证：
   - 所有搜索条件被清空
   - 部门树选中状态被清除
   - 分页重置到第一页
   - 司机列表重新加载

### 场景2: 表单重置测试
1. 点击"新增"按钮打开表单
2. 填写部分表单字段
3. 触发表单验证错误（如必填项为空）
4. 点击"取消"按钮
5. 重新打开表单
6. 验证：
   - 表单字段恢复到默认值
   - 验证错误信息被清除
   - 表单状态正常

### 场景3: 修改表单重置测试
1. 选择一个司机点击"修改"
2. 修改部分字段内容
3. 点击"取消"按钮
4. 重新打开同一司机的修改表单
5. 验证：
   - 表单显示原始数据
   - 没有保留之前的修改内容

## 与标准页面的一致性

现在司机管理页面的重置功能与 `system/user/index.vue` 保持一致：

1. **表单引用命名**: `queryFormRef`, `driverFormRef`
2. **重置方法**: 使用 `resetFields()` 和 `clearValidate()`
3. **查询重置**: 包含分页重置和树状态清除
4. **错误处理**: 使用可选链操作符 `?.` 避免空引用错误

## 优势

1. **标准化**: 与系统其他页面保持一致的代码风格
2. **可靠性**: 使用Element Plus官方推荐的重置方式
3. **完整性**: 同时重置数据和验证状态
4. **用户体验**: 提供完整的重置功能，避免残留状态
