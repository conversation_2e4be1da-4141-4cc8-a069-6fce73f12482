# 司机管理页面 API 调用测试

## 部门树接口调用

### 接口信息
- **接口路径**: `/system/user/deptTree`
- **请求方法**: GET
- **返回类型**: `DeptTreeVO[]`

### 接口调用代码
```javascript
import { deptTreeSelect } from "@/api/system/user";

// 获取部门树数据
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    console.log('部门树数据:', res.data);
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取部门树失败:', error);
    // 使用模拟数据作为备选方案
    deptOptions.value = mockDeptData;
  }
}
```

### 数据结构示例
```javascript
// DeptTreeVO 接口定义
interface DeptTreeVO {
  id: number | string;
  label: string;
  parentId: number | string;
  weight: number;
  children: DeptTreeVO[];
  disabled: boolean;
}

// 返回数据示例
[
  {
    id: 1,
    label: "总公司",
    parentId: 0,
    weight: 1,
    disabled: false,
    children: [
      {
        id: 2,
        label: "运营部",
        parentId: 1,
        weight: 1,
        disabled: false,
        children: [
          {
            id: 3,
            label: "第一车队",
            parentId: 2,
            weight: 1,
            disabled: false,
            children: []
          }
        ]
      }
    ]
  }
]
```

### 组件使用
```vue
<template>
  <!-- 部门树组件 -->
  <el-tree
    ref="deptTreeRef"
    node-key="id"
    :data="deptOptions"
    :props="{ label: 'label', children: 'children' }"
    @node-click="handleNodeClick"
  />
  
  <!-- 部门选择器 -->
  <el-tree-select
    v-model="form.deptId"
    :data="deptOptions"
    :props="{ label: 'label', children: 'children', value: 'id' }"
    placeholder="请选择所属部门"
  />
</template>
```

### 错误处理
1. **网络错误**: 使用模拟数据作为备选方案
2. **权限错误**: 显示相应的错误提示
3. **数据格式错误**: 进行数据验证和转换

### 测试步骤
1. 打开浏览器开发者工具
2. 访问司机管理页面
3. 查看 Network 面板中的 `/system/user/deptTree` 请求
4. 验证返回的数据格式是否正确
5. 测试部门树的交互功能

### 注意事项
- 确保用户有访问部门树的权限
- 处理空数据的情况
- 考虑大量部门数据的性能优化
- 实现适当的加载状态提示
