<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="线路" prop="routeId">
        <el-select v-model="queryParams.routeId" placeholder="请选择线路" clearable>
          <el-option
            v-for="route in routeOptions"
            :key="route.routeId"
            :label="route.routeName"
            :value="route.routeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="operationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="参数ID" align="center" prop="operationId" />
      <el-table-column label="线路" align="center" prop="routeName" />
      <el-table-column label="首班车时间" align="center" prop="firstBusTime" />
      <el-table-column label="末班车时间" align="center" prop="lastBusTime" />
      <el-table-column label="发车间隔(分钟)" align="center" prop="departureInterval" />
      <el-table-column label="关联发车时间(分钟)" align="center" prop="linkedDepartureTime" />
      <el-table-column label="单程运行时间(分钟)" align="center" prop="singleTripTime" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '1'" type="success">启用</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改运营参数对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="operationRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路" prop="routeId">
              <el-select v-model="form.routeId" placeholder="请选择线路" @change="handleRouteChange">
                <el-option
                  v-for="route in routeOptions"
                  :key="route.routeId"
                  :label="route.routeName"
                  :value="route.routeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="启用" value="1" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="首班车时间" prop="firstBusTime">
              <el-time-picker
                v-model="form.firstBusTime"
                placeholder="选择首班车时间"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="末班车时间" prop="lastBusTime">
              <el-time-picker
                v-model="form.lastBusTime"
                placeholder="选择末班车时间"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发车间隔(分钟)" prop="departureInterval">
              <el-input-number v-model="form.departureInterval" :min="1" :max="60" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联发车时间(分钟)" prop="linkedDepartureTime">
              <el-input-number v-model="form.linkedDepartureTime" :min="0" :max="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单程运行时间(分钟)" prop="singleTripTime">
              <el-input-number v-model="form.singleTripTime" :min="10" :max="300" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大载客量" prop="maxPassengerCapacity">
              <el-input-number v-model="form.maxPassengerCapacity" :min="10" :max="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="平均速度(km/h)" prop="averageSpeed">
              <el-input-number v-model="form.averageSpeed" :precision="1" :min="5" :max="80" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最高速度(km/h)" prop="maxSpeed">
              <el-input-number v-model="form.maxSpeed" :precision="1" :min="10" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="运营时间说明" prop="operationTimeDescription">
          <el-input v-model="form.operationTimeDescription" type="textarea" :rows="2" placeholder="请输入运营时间说明" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Operation">
const { proxy } = getCurrentInstance();

const operationList = ref([]);
const routeOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    routeId: null,
    status: null
  },
  rules: {
    routeId: [
      { required: true, message: "请选择线路", trigger: "change" }
    ],
    firstBusTime: [
      { required: true, message: "请选择首班车时间", trigger: "change" }
    ],
    lastBusTime: [
      { required: true, message: "请选择末班车时间", trigger: "change" }
    ],
    departureInterval: [
      { required: true, message: "请输入发车间隔", trigger: "blur" }
    ],
    singleTripTime: [
      { required: true, message: "请输入单程运行时间", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询运营参数列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      operationId: 1,
      routeId: 1,
      routeName: "1路",
      firstBusTime: "05:30",
      lastBusTime: "22:30",
      departureInterval: 8,
      linkedDepartureTime: 3,
      singleTripTime: 45,
      maxPassengerCapacity: 35,
      averageSpeed: 22.5,
      maxSpeed: 45.0,
      operationTimeDescription: "工作日运营时间：05:30-22:30，节假日运营时间：06:00-22:00",
      status: "1",
      remark: "主干线路，客流量大"
    },
    {
      operationId: 2,
      routeId: 2,
      routeName: "2路",
      firstBusTime: "06:00",
      lastBusTime: "21:30",
      departureInterval: 12,
      linkedDepartureTime: 5,
      singleTripTime: 55,
      maxPassengerCapacity: 30,
      averageSpeed: 25.0,
      maxSpeed: 50.0,
      operationTimeDescription: "机场专线，全年无休",
      status: "1",
      remark: "机场专线，票价较高"
    },
    {
      operationId: 3,
      routeId: 3,
      routeName: "3路",
      firstBusTime: "06:30",
      lastBusTime: "20:00",
      departureInterval: 15,
      linkedDepartureTime: 2,
      singleTripTime: 40,
      maxPassengerCapacity: 40,
      averageSpeed: 20.0,
      maxSpeed: 40.0,
      operationTimeDescription: "临时停运维护中",
      status: "0",
      remark: "线路维护中，暂停运营"
    }
  ];
  
  setTimeout(() => {
    operationList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

/** 获取线路选项 */
function getRouteOptions() {
  routeOptions.value = [
    { routeId: 1, routeName: "1路" },
    { routeId: 2, routeName: "2路" },
    { routeId: 3, routeName: "3路" }
  ];
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.operationId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getRouteOptions();
  open.value = true;
  title.value = "添加运营参数";
}

function handleUpdate(row) {
  reset();
  getRouteOptions();
  form.value = { ...row };
  open.value = true;
  title.value = "修改运营参数";
}

function handleDelete(row) {
  const operationIds = row.operationId || ids.value;
  proxy.$modal.confirm('是否确认删除运营参数编号为"' + operationIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有运营参数数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function handleRouteChange(routeId) {
  const route = routeOptions.value.find(r => r.routeId === routeId);
  if (route) {
    form.value.routeName = route.routeName;
  }
}

function submitForm() {
  proxy.$refs["operationRef"].validate(valid => {
    if (valid) {
      if (form.value.operationId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    operationId: null,
    routeId: null,
    routeName: null,
    firstBusTime: null,
    lastBusTime: null,
    departureInterval: 10,
    linkedDepartureTime: 3,
    singleTripTime: null,
    maxPassengerCapacity: 35,
    averageSpeed: 25.0,
    maxSpeed: 45.0,
    operationTimeDescription: null,
    status: "1",
    remark: null
  };
  proxy.resetForm("operationRef");
}

onMounted(() => {
  getRouteOptions();
  getList();
});
</script>
